from retreival import get_today_date, qna_search, product_search
from tools import get_all_tools, extract_and_save_user_info, get_user_info
import os
from dotenv import load_dotenv

from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain.chat_models import init_chat_model
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_mongodb.chat_message_histories import MongoDBChatMessageHistory
load_dotenv(override=True)

# Verify OpenAI API key is loaded
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

# Combine retrieval tools with LangMem memory tools
tools = [get_today_date, qna_search, product_search] + get_all_tools()

# Simple model setup
model = init_chat_model(
    model="gpt-4o-mini",
    temperature=0.1,
    api_key=OPENAI_API_KEY,
)


# Simple prompt with LangMem
prompt = ChatPromptTemplate.from_messages([
    ("system", """You are a helpful assistant with LangMem memory capabilities.

TOOLS:
- product_search: Find courses/products
- qna_search: Technical support
- get_today_date: Current date/time
- save_user_info: Save user contact details
- get_user_profile: View saved user info
- book_service: Book services (auto-uses saved info)
- search_bookings: Find previous bookings
- delete_booking: Delete/cancel a booking by ID
- cancel_booking: Cancel a booking by ID (alias for delete)
- user_memory/search_user_memory: LangMem tools for user data
- booking_memory/search_booking_memory: LangMem tools for bookings

USAGE:
- Product questions → product_search
- Tech issues → qna_search
- Booking → book_service (auto-gets saved user info)
- Save contact info → save_user_info
- View profile → get_user_profile
- Booking history → search_bookings
- Delete/cancel booking → delete_booking or cancel_booking (provide booking ID)

When user provides name/email/phone, automatically save it.
For bookings, get saved user info first, ask for missing details if needed.
For deletions, extract booking ID from user request."""),
    ("placeholder", "{chat_history}"),
    ("human", "{input}"),
    ("placeholder", "{agent_scratchpad}"),
])

# Simple MongoDB session history
def get_session_history(session_id: str) -> MongoDBChatMessageHistory:
    return MongoDBChatMessageHistory(
        connection_string=os.getenv("MONGO_URI", "mongodb://localhost:27017/"),
        database_name="test_agent_db",
        collection_name="chat_history",
        session_id=session_id
    )

agent = create_tool_calling_agent(model, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

# Wrap agent with MongoDB-backed chat history
agent_with_chat_history = RunnableWithMessageHistory(
    agent_executor,
    get_session_history,  # Function that returns MongoDB chat history
    input_messages_key="input",
    history_messages_key="chat_history",
)

print("🧠 Simple LangMem Chat")
print("=" * 40)
print("Features:")
print("- Product/QNA search")
print("- Auto-save user info")
print("- Book services")
print("- Type 'profile' for saved info")
print("- Type 'exit' to quit")
print("=" * 40)

session_id = "langmem-001"
print(f"📋 Session: {session_id}")

while True:
    try:
        user_input = input(f"\n[{session_id[-3:]}] User: ")

        if user_input.lower() in ["exit", "quit"]:
            print("👋 Bye! Info saved.")
            break
        elif user_input.lower() == "profile":
            user_info = get_user_info()
            if any(user_info.values()):
                print(f"\n👤 Profile:")
                for key, value in user_info.items():
                    if value:
                        print(f"   {key.title()}: {value}")
            else:
                print("\n👤 No profile saved.")
            continue

        # Auto-extract user info
        auto_save = extract_and_save_user_info(user_input)
        if auto_save:
            print(auto_save)

        # Process with agent
        print("🤔 Processing...")
        response = agent_with_chat_history.invoke(
            {"input": user_input},
            config={"configurable": {"session_id": session_id}}
        )

        print(f"\n🤖 {response['output']}")

    except KeyboardInterrupt:
        print("\n👋 Bye!")
        break
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")