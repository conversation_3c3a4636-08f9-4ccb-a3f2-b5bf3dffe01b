#!/usr/bin/env python3
"""
Test tools directly
"""

from tools import save_user_info, get_user_info, book_service, extract_and_save_user_info

print("Testing LangMem tools directly...")
print("=" * 50)

# Test 1: Direct save_user_info using invoke
print("1. Testing save_user_info with invoke:")
result = save_user_info.invoke({"name": "<PERSON>", "email": "<EMAIL>", "phone": "9841234567"})
print(f"Result: {result}")

print("\n2. Testing get_user_info:")
info = get_user_info.invoke({})
print(f"Retrieved info: {info}")

print("\n3. Testing extract_and_save_user_info:")
text = "Hi, my name is <PERSON>, email <EMAIL>, phone 9851234567"
result = extract_and_save_user_info(text)
print(f"Auto-extract result: {result}")

print("\n4. Testing get_user_info after auto-extract:")
info = get_user_info.invoke({})
print(f"Retrieved info: {info}")

print("\n5. Testing book_service:")
result = book_service.invoke({"service_name": "Test Course"})
print(f"Booking result: {result}")

print("\n" + "=" * 50)
print("Direct tool testing complete!")
