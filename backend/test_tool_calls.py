#!/usr/bin/env python3
"""
Test tool call integration
"""

import os
import sys
from dotenv import load_dotenv

# Add src to path
sys.path.append('src')

load_dotenv(override=True)

from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain.chat_models import init_chat_model
from langchain_core.prompts import Chat<PERSON>romptTemplate
from api.services.retreival import get_today_date, qna_search, product_search
from api.services.tools import get_all_tools

# Verify OpenAI API key is loaded
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

# Simple model setup
model = init_chat_model(
    model="gpt-4o-mini",
    temperature=0.1,
    api_key=OPENAI_API_KEY,
)

# Use only basic tools for testing (avoid dynamic memory tools that need UserTenantDB)
tools = [get_today_date, qna_search, product_search]

# Simple prompt
prompt = ChatPromptTemplate.from_messages([
    ("system", """You are a helpful assistant with tool capabilities.
    
TOOLS:
- product_search: Find courses/products
- qna_search: Technical support
- get_today_date: Current date/time
- save_user_info: Save user contact details
- get_user_profile: View saved user info
- book_service: Book services (auto-uses saved info)
- search_bookings: Find previous bookings
- delete_booking: Delete/cancel a booking by ID
- cancel_booking: Cancel a booking by ID (alias for delete)

When user provides name/email/phone, automatically save it.
For bookings, get saved user info first, ask for missing details if needed."""),
    ("human", "{input}"),
    ("placeholder", "{agent_scratchpad}"),
])

# Create agent with return_intermediate_steps=True
agent = create_tool_calling_agent(model, tools, prompt)
agent_executor = AgentExecutor(
    agent=agent, 
    tools=tools, 
    verbose=True,
    return_intermediate_steps=True
)

def test_tool_calls():
    """Test tool call integration"""
    print("Testing tool call integration...")
    print("=" * 50)
    
    # Test message that should trigger tool calls
    test_message = "What's today's date and search for Python courses?"
    
    print(f"Input: {test_message}")
    print("-" * 30)
    
    try:
        # Execute agent
        result = agent_executor.invoke({"input": test_message})
        
        print(f"Output: {result.get('output', 'No output')}")
        print("-" * 30)
        
        # Check intermediate steps
        intermediate_steps = result.get("intermediate_steps", [])
        print(f"Intermediate steps found: {len(intermediate_steps)}")
        
        for i, step in enumerate(intermediate_steps):
            if len(step) == 2:
                agent_action, tool_output = step
                print(f"Step {i+1}:")
                print(f"  Tool: {getattr(agent_action, 'tool', 'unknown')}")
                print(f"  Input: {getattr(agent_action, 'tool_input', {})}")
                print(f"  Output: {str(tool_output)[:100]}...")
                print()
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    success = test_tool_calls()
    if success:
        print("✅ Tool call integration test passed!")
    else:
        print("❌ Tool call integration test failed!")
