#!/usr/bin/env python3
"""
Test API tool call integration
"""

import requests
import json

# Test configuration
BASE_URL = "http://localhost:8000"
API_KEY = "test-api-key"  # From the test auth setup

def test_chat_with_tools():
    """Test chat API with tool calls"""
    print("Testing chat API with tool calls...")
    print("=" * 50)
    
    # Headers for authentication
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    # Test message that should trigger tool calls
    test_message = "What's today's date and search for Python courses?"
    
    payload = {
        "message": test_message
    }
    
    print(f"Sending message: {test_message}")
    print("-" * 30)
    
    try:
        # Make API request
        response = requests.post(
            f"{BASE_URL}/api/v1/chat",
            json=payload,
            headers=headers,
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"✅ Response received!")
            print(f"Response: {data.get('response', 'No response')}")
            print("-" * 30)
            
            # Check tool calls
            tools_used = data.get('tools_used', [])
            print(f"Tools used: {len(tools_used)}")
            
            for i, tool in enumerate(tools_used):
                print(f"Tool {i+1}:")
                print(f"  Name: {tool.get('name', 'unknown')}")
                print(f"  Description: {tool.get('description', 'N/A')}")
                print(f"  Input: {tool.get('input', {})}")
                print(f"  Output: {str(tool.get('output', ''))[:100]}...")
                print()
            
            return True
            
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    success = test_chat_with_tools()
    if success:
        print("✅ API tool call integration test passed!")
    else:
        print("❌ API tool call integration test failed!")
