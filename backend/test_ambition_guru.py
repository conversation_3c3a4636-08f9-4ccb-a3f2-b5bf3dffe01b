#!/usr/bin/env python3
"""
Test script for Ambition Guru Customer Service Representative
Tests the new prompt and tools implementation
"""

def test_tools_import():
    """Test if our new tools can be imported"""
    try:
        from src.api.services.retreival import search_database, create_issue_tickets, get_today_date
        print("✅ Tools imported successfully:")
        print(f"  - search_database: {search_database.__doc__}")
        print(f"  - create_issue_tickets: {create_issue_tickets.__doc__}")
        print(f"  - get_today_date: {get_today_date.__doc__}")
        return True
    except Exception as e:
        print(f"❌ Error importing tools: {e}")
        return False

def test_create_ticket():
    """Test the create_issue_tickets tool"""
    try:
        from src.api.services.retreival import create_issue_tickets
        
        # Test ticket creation
        result = create_issue_tickets(
            name="Test Student",
            contact_number="9841234567",
            description="Test issue for IELTS course inquiry"
        )
        print("✅ Ticket creation test:")
        print(f"  Result: {result}")
        return True
    except Exception as e:
        print(f"❌ Error testing ticket creation: {e}")
        return False

def test_prompt_structure():
    """Test if the prompt is properly structured"""
    try:
        from src.api.services.test import prompt
        
        print("✅ Prompt structure test:")
        print(f"  Prompt type: {type(prompt)}")
        print(f"  Messages count: {len(prompt.messages)}")
        
        # Check if the prompt contains Ambition Guru content
        system_message = str(prompt.messages[0])
        if "Ambition Guru" in system_message:
            print("  ✅ Contains Ambition Guru branding")
        else:
            print("  ❌ Missing Ambition Guru branding")
            
        if "search_database" in system_message:
            print("  ✅ Contains search_database tool reference")
        else:
            print("  ❌ Missing search_database tool reference")
            
        if "create_issue_tickets" in system_message:
            print("  ✅ Contains create_issue_tickets tool reference")
        else:
            print("  ❌ Missing create_issue_tickets tool reference")
            
        return True
    except Exception as e:
        print(f"❌ Error testing prompt: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Ambition Guru Implementation")
    print("=" * 50)
    
    tests = [
        ("Tools Import", test_tools_import),
        ("Ticket Creation", test_create_ticket),
        ("Prompt Structure", test_prompt_structure),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Ambition Guru implementation is ready!")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
