#!/usr/bin/env python3
"""
Test phone number validation
"""

import re

# Test the phone pattern
PHONE_PATTERN = r'\b(?:\+977[-.\s]?)?([9][0-9]{9})\b'

test_cases = [
    "My phone is 9841234567",
    "Call me at 9801234567",
    "Phone: 9851234567",
    "Contact: +977 9841234567",
    "My number is 98277y name",  # This should NOT match
    "Phone 1234567890",  # This should NOT match (doesn't start with 9)
    "9812345678",  # This should NOT match (only 9 digits)
    "98123456789",  # This should match (10 digits starting with 9)
]

print("Testing phone number extraction:")
print("=" * 50)

for test in test_cases:
    phones = re.findall(PHONE_PATTERN, test)
    if phones:
        phone = phones[0]
        if len(phone) == 10 and phone.startswith('9'):
            print(f"✅ '{test}' -> Found valid phone: {phone}")
        else:
            print(f"❌ '{test}' -> Found invalid phone: {phone}")
    else:
        print(f"❌ '{test}' -> No phone found")

print("\n" + "=" * 50)
print("Testing phone cleaning:")

def clean_and_validate_phone(phone_input):
    """Clean and validate phone number"""
    clean_phone = re.sub(r'[^\d]', '', phone_input)
    if len(clean_phone) == 10 and clean_phone.startswith('9'):
        return clean_phone
    return None

phone_tests = [
    "9841234567",
    "************",
    "************",
    "+977 9841234567",
    "98277y",
    "1234567890",
    "984123456",
]

for phone in phone_tests:
    result = clean_and_validate_phone(phone)
    if result:
        print(f"✅ '{phone}' -> {result}")
    else:
        print(f"❌ '{phone}' -> Invalid")
