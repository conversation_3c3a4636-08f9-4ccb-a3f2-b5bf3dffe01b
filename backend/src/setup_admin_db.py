"""
Setup script for admin database and multi-tenant system
Creates the chatbot_system admin database with tenants collection and dummy data
"""

import os
import argparse
from pymongo import MongoClient
from bson import ObjectId
from datetime import datetime
from dotenv import load_dotenv
from argon2 import PasswordHasher

load_dotenv()

# Initialize password hasher
ph = PasswordHasher()

def check_tenant_exists(client, tenant_slug):
    """Check if a tenant already exists"""
    admin_db = client["chatbot_system"]
    tenants_collection = admin_db.tenants

    existing_tenant = tenants_collection.find_one({"slug": tenant_slug})
    return existing_tenant

def setup_admin_database(force_recreate=False, tenant_config=None):
    """Setup the admin database with tenants collection"""

    # Connect to MongoDB
    mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017")
    client = MongoClient(mongo_uri)

    # Create admin database
    admin_db = client["chatbot_system"]

    print("🔧 Setting up admin database: chatbot_system")

    # Create tenants collection
    tenants_collection = admin_db.tenants

    # Use provided tenant config or default
    if tenant_config is None:
        tenant_config = {
            "name": "Ambition Guru",
            "slug": "ambition-guru",
            "database_name": "ambition_guru_db",
            "label": "Ambition Guru Education",
            "domain": "ambition-guru.com"
        }

    # Check if tenant already exists
    existing_tenant = check_tenant_exists(client, tenant_config["slug"])

    if existing_tenant and not force_recreate:
        print(f"✅ Tenant '{tenant_config['slug']}' already exists (ID: {existing_tenant['_id']})")
        print("   Use --force to recreate or --add-tenant to add a new one")
        return [existing_tenant]

    if force_recreate and existing_tenant:
        # Clear existing data (for fresh setup)
        tenants_collection.delete_many({"slug": tenant_config["slug"]})
        print(f"🗑️  Cleared existing tenant data for '{tenant_config['slug']}'")
    elif not existing_tenant:
        print(f"🆕 Creating new tenant: {tenant_config['slug']}")
    
    # Create tenant document
    tenant_doc = {
        "_id": ObjectId(),
        "name": tenant_config["name"],
        "slug": tenant_config["slug"],
        "database_name": tenant_config["database_name"],
        "label": tenant_config["label"],
        "domain": tenant_config["domain"],
        "created_at": datetime.now(datetime.UTC),
        "status": "active",
        "settings": {
            "max_users": 100,
            "features": ["chat", "booking", "search"]
        }
    }

    # Insert tenant
    result = tenants_collection.insert_one(tenant_doc)
    print(f"✅ Created tenant with ID: {result.inserted_id}")

    # Print tenant information
    print(f"   📋 {tenant_doc['name']} (slug: {tenant_doc['slug']}, db: {tenant_doc['database_name']})")

    return [tenant_doc]


def check_tenant_database_exists(client, db_name):
    """Check if tenant database and collections exist"""
    tenant_db = client[db_name]

    # Check if users collection exists and has data
    users_count = tenant_db.users.count_documents({})
    settings_count = tenant_db.settings.count_documents({})

    return {
        "users_exist": users_count > 0,
        "settings_exist": settings_count > 0,
        "users_count": users_count,
        "settings_count": settings_count
    }

def setup_tenant_databases(tenants, force_recreate=False):
    """Setup individual tenant databases with users and settings"""

    mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017")
    client = MongoClient(mongo_uri)

    for tenant in tenants:
        db_name = tenant["database_name"]
        tenant_db = client[db_name]

        print(f"\n🔧 Setting up tenant database: {db_name}")

        # Check if database already exists
        db_status = check_tenant_database_exists(client, db_name)

        if (db_status["users_exist"] or db_status["settings_exist"]) and not force_recreate:
            print(f"   ✅ Database already initialized:")
            print(f"      - Users: {db_status['users_count']}")
            print(f"      - Settings: {db_status['settings_count']}")
            print(f"   Use --force to recreate")
            continue

        if force_recreate:
            # Clear existing data
            tenant_db.users.delete_many({})
            tenant_db.settings.delete_many({})
            tenant_db.invitations.delete_many({})
            print(f"   🗑️  Cleared existing data")

        # Create users collection with dummy users
        users_data = [
            {
                "_id": ObjectId(),
                "username": "admin",
                "hashed_password": ph.hash("admin123"),
                "role": "admin",
                "email": f"admin@{tenant['domain']}",
                "created_at": datetime.now(datetime.UTC),
                "status": "active"
            },
            {
                "_id": ObjectId(),
                "username": "supervisor",
                "hashed_password": ph.hash("supervisor123"),
                "role": "supervisor",
                "email": f"supervisor@{tenant['domain']}",
                "created_at": datetime.now(datetime.UTC),
                "status": "active"
            },
            {
                "_id": ObjectId(),
                "username": "agent1",
                "hashed_password": ph.hash("agent123"),
                "role": "agent",
                "email": f"agent1@{tenant['domain']}",
                "created_at": datetime.now(datetime.UTC),
                "status": "active"
            }
        ]
        
        tenant_db.users.insert_many(users_data)
        print(f"   👥 Created {len(users_data)} users")
        
        # Create settings collection
        settings_data = [
            {
                "name": "jwt_config",
                "secret_key": os.getenv("SECRET_KEY", "abcd"),
                "algorithm": "HS256"
            },
            {
                "name": "token_validity",
                "days": 0,
                "hours": 6,
                "minutes": 0,
                "seconds": 0
            },
            {
                "name": "role_hierarchy",
                "roles": {
                    "admin": 3,
                    "supervisor": 2,
                    "agent": 1
                }
            },
            {
                "name": "nav_permission",
                "admin": {
                    "dashboard": True,
                    "users": True,
                    "chat": True,
                    "settings": True
                },
                "supervisor": {
                    "dashboard": True,
                    "users": True,
                    "chat": True,
                    "settings": False
                },
                "agent": {
                    "dashboard": True,
                    "users": False,
                    "chat": True,
                    "settings": False
                }
            },
            {
                "name": "env",
                "qdrant_config": {
                    "info_collection": "information",
                    "product_collection": "products",
                    "qdrant_url": "http://localhost:6333",
                    "qdrant_api_key": None
                },
                "minio_config": {
                    "access_key": "minioadmin",
                    "secret_key": "minioadmin",
                    "minio_url": "localhost:9000",
                    "bucket_name": f"{tenant['slug']}-bucket"
                },
                "openai_config": {
                    "embedding_model": "text-embedding-3-large",
                    "embedding_dimensions": 1536
                }
            }
        ]
        
        tenant_db.settings.insert_many(settings_data)
        print(f"   ⚙️  Created {len(settings_data)} settings")
        
        # Create indexes
        tenant_db.users.create_index("username", unique=True)
        tenant_db.users.create_index("email", unique=True)
        tenant_db.invitations.create_index("token", unique=True)
        
        print(f"   📊 Created database indexes")


def print_tenant_info(tenants):
    """Print tenant information for testing"""
    
    print("\n" + "="*60)
    print("🎯 MULTI-TENANT SYSTEM SETUP COMPLETE")
    print("="*60)
    
    print("\n📋 TENANT INFORMATION:")
    for tenant in tenants:
        print(f"\n🏢 {tenant['name']}")
        print(f"   Slug: {tenant['slug']}")
        print(f"   Database: {tenant['database_name']}")
        print(f"   Domain: {tenant['domain']}")
        print(f"   Tenant ID: {tenant['_id']}")
        
        print(f"\n   👥 Test Users:")
        print(f"   - admin / admin123 (role: admin)")
        print(f"   - supervisor / supervisor123 (role: supervisor)")
        print(f"   - agent1 / agent123 (role: agent)")
    
    print(f"\n🔗 API ENDPOINTS:")
    print(f"   Login: POST /api/v1/login")
    print(f"   Chat: POST /api/v1/chat")
    print(f"   Verify Token: GET /api/v1/verify_token")
    
    print(f"\n📝 LOGIN EXAMPLE:")
    print(f"   {{")
    print(f"     \"username\": \"admin\",")
    print(f"     \"password\": \"admin123\",")
    print(f"     \"client_id\": \"ambition-guru\"")
    print(f"   }}")


def add_new_tenant(tenant_config):
    """Add a new tenant to the system"""
    print(f"🆕 Adding new tenant: {tenant_config['slug']}")

    # Setup admin database with new tenant
    tenants = setup_admin_database(force_recreate=False, tenant_config=tenant_config)

    # Setup tenant database
    setup_tenant_databases(tenants, force_recreate=False)

    return tenants

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Multi-tenant system setup")

    parser.add_argument("--force", action="store_true",
                       help="Force recreate existing tenants and databases")

    parser.add_argument("--check", action="store_true",
                       help="Check current tenant status without making changes")

    parser.add_argument("--add-tenant", action="store_true",
                       help="Add a new tenant (use with --name, --slug, --domain)")

    parser.add_argument("--name", type=str,
                       help="Tenant name (for --add-tenant)")

    parser.add_argument("--slug", type=str,
                       help="Tenant slug (for --add-tenant)")

    parser.add_argument("--domain", type=str,
                       help="Tenant domain (for --add-tenant)")

    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()

    print("🚀 Starting multi-tenant system setup...")

    if args.check:
        # Just check status
        mongo_uri = os.getenv("MONGO_URI", "mongodb://localhost:27017")
        client = MongoClient(mongo_uri)
        admin_db = client["chatbot_system"]

        tenants = list(admin_db.tenants.find({}))
        if tenants:
            print(f"\n📋 Found {len(tenants)} existing tenants:")
            for tenant in tenants:
                print(f"   🏢 {tenant['name']} (slug: {tenant['slug']}, ID: {tenant['_id']})")

                # Check tenant database status
                db_status = check_tenant_database_exists(client, tenant["database_name"])
                print(f"      Database: {tenant['database_name']}")
                print(f"      Users: {db_status['users_count']}, Settings: {db_status['settings_count']}")
        else:
            print("❌ No tenants found in the system")

    elif args.add_tenant:
        # Add new tenant
        if not all([args.name, args.slug, args.domain]):
            print("❌ Error: --add-tenant requires --name, --slug, and --domain")
            exit(1)

        tenant_config = {
            "name": args.name,
            "slug": args.slug,
            "database_name": f"{args.slug.replace('-', '_')}_db",
            "label": args.name,
            "domain": args.domain
        }

        tenants = add_new_tenant(tenant_config)
        print_tenant_info(tenants)

    else:
        # Default setup
        tenants = setup_admin_database(force_recreate=args.force)

        # Setup individual tenant databases
        setup_tenant_databases(tenants, force_recreate=args.force)

        # Print information
        print_tenant_info(tenants)

    print("\n✅ Multi-tenant system setup completed successfully!")
    print("🔧 You can now start the FastAPI application and test the endpoints.")
