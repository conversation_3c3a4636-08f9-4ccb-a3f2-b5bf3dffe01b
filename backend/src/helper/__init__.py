"""
Helper modules for the application
"""

from bson import ObjectId
from typing import Any, Dict, List, Union


def convert_objectid_to_str(data: Union[Dict[str, Any], List[Dict[str, Any]]]) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
    """
    Convert ObjectId fields to strings in MongoDB documents
    
    Args:
        data: Single document or list of documents from MongoDB
        
    Returns:
        Document(s) with ObjectId fields converted to strings
    """
    if isinstance(data, list):
        return [convert_objectid_to_str(item) for item in data]
    
    if isinstance(data, dict):
        result = {}
        for key, value in data.items():
            if isinstance(value, ObjectId):
                result[key] = str(value)
            elif isinstance(value, dict):
                result[key] = convert_objectid_to_str(value)
            elif isinstance(value, list):
                result[key] = convert_objectid_to_str(value)
            else:
                result[key] = value
        return result
    
    return data


__all__ = ['convert_objectid_to_str']
