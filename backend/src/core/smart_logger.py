"""
Smart logging configuration for the chatbot system
Reduces noise and organizes logs by importance and context
"""

import logging
import os
from typing import Dict, List, Optional
from utils.colored_logger import Colors, ColoredFormatter

class SmartLogger:
    """Smart logger that organizes logs by context and importance"""
    
    # Define log categories with their importance levels
    LOG_CATEGORIES = {
        'STARTUP': {'level': logging.INFO, 'emoji': '🚀', 'color': Colors.BRIGHT_GREEN},
        'AUTH': {'level': logging.INFO, 'emoji': '🔐', 'color': Colors.BRIGHT_BLUE},
        'CHAT': {'level': logging.INFO, 'emoji': '💬', 'color': Colors.BRIGHT_CYAN},
        'AGENT': {'level': logging.INFO, 'emoji': '🤖', 'color': Colors.BRIGHT_MAGENTA},
        'DATABASE': {'level': logging.WARNING, 'emoji': '🗄️', 'color': Colors.YELLOW},
        'CACHE': {'level': logging.WARNING, 'emoji': '⚡', 'color': Colors.BRIGHT_YELLOW},
        'ERROR': {'level': logging.ERROR, 'emoji': '❌', 'color': Colors.BRIGHT_RED},
        'SUCCESS': {'level': logging.INFO, 'emoji': '✅', 'color': Colors.BRIGHT_GREEN},
        'DEBUG': {'level': logging.DEBUG, 'emoji': '🔍', 'color': Colors.BRIGHT_BLACK},
    }
    
    # Modules to suppress or reduce logging
    NOISE_MODULES = {
        'watchfiles.main': logging.WARNING,
        'uvicorn.access': logging.WARNING,
        'httpx': logging.WARNING,
        'httpcore': logging.WARNING,
        'urllib3': logging.WARNING,
        'pymongo': logging.WARNING,
        'motor': logging.WARNING,
    }
    
    def __init__(self, log_level: str = "INFO"):
        self.log_level = getattr(logging, log_level.upper(), logging.INFO)
        self.setup_logging()
    
    def setup_logging(self):
        """Setup smart logging configuration"""
        # Create custom formatter
        formatter = SmartFormatter()
        
        # Setup console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(self.log_level)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # Remove existing handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Add our smart handler
        root_logger.addHandler(console_handler)
        
        # Reduce noise from specific modules
        for module, level in self.NOISE_MODULES.items():
            logging.getLogger(module).setLevel(level)
    
    @classmethod
    def log_startup(cls, message: str):
        """Log startup events"""
        cls._log_with_category('STARTUP', message)
    
    @classmethod
    def log_auth(cls, message: str):
        """Log authentication events"""
        cls._log_with_category('AUTH', message)
    
    @classmethod
    def log_chat(cls, message: str):
        """Log chat-related events"""
        cls._log_with_category('CHAT', message)
    
    @classmethod
    def log_agent(cls, message: str):
        """Log agent-related events"""
        cls._log_with_category('AGENT', message)
    
    @classmethod
    def log_database(cls, message: str):
        """Log database events (only warnings and errors)"""
        cls._log_with_category('DATABASE', message)
    
    @classmethod
    def log_cache(cls, message: str):
        """Log cache events (only warnings and errors)"""
        cls._log_with_category('CACHE', message)
    
    @classmethod
    def log_error(cls, message: str):
        """Log error events"""
        cls._log_with_category('ERROR', message)
    
    @classmethod
    def log_success(cls, message: str):
        """Log success events"""
        cls._log_with_category('SUCCESS', message)
    
    @classmethod
    def log_debug(cls, message: str):
        """Log debug events"""
        cls._log_with_category('DEBUG', message)
    
    @classmethod
    def _log_with_category(cls, category: str, message: str):
        """Internal method to log with category"""
        config = cls.LOG_CATEGORIES.get(category, cls.LOG_CATEGORIES['DEBUG'])
        logger = logging.getLogger(f"chatbot.{category.lower()}")
        
        # Format message with emoji and color
        emoji = config['emoji']
        formatted_message = f"{emoji} {message}"
        
        # Log at appropriate level
        logger.log(config['level'], formatted_message)

class SmartFormatter(ColoredFormatter):
    """Custom formatter for smart logging"""
    
    def format(self, record):
        # Simplify logger names
        if record.name.startswith('chatbot.'):
            record.name = record.name.replace('chatbot.', '').upper()
        
        # Use a cleaner format
        if hasattr(record, 'funcName') and record.funcName != '<module>':
            format_string = '%(name)s | %(message)s'
        else:
            format_string = '%(name)s | %(message)s'
        
        # Apply the format
        formatter = logging.Formatter(format_string)
        formatted = formatter.format(record)
        
        # Add colors
        color = self.COLORS.get(record.levelname, '')
        if color:
            return f"{color}{formatted}{Colors.RESET}"
        return formatted

# Global smart logger instance
smart_logger = SmartLogger(os.getenv("LOG_LEVEL", "INFO"))

# Convenience functions for easy import
log_startup = SmartLogger.log_startup
log_auth = SmartLogger.log_auth
log_chat = SmartLogger.log_chat
log_agent = SmartLogger.log_agent
log_database = SmartLogger.log_database
log_cache = SmartLogger.log_cache
log_error = SmartLogger.log_error
log_success = SmartLogger.log_success
log_debug = SmartLogger.log_debug
