"""
Simple pagination utilities for API responses
"""

from typing import Generic, TypeVar, List, Any, Dict
from pydantic import BaseModel, Field
import math

T = TypeVar('T')

class PaginationMeta(BaseModel):
    """Pagination metadata"""
    page: int
    limit: int
    total_items: int
    total_pages: int
    has_next: bool
    has_prev: bool

class PaginatedResponse(BaseModel, Generic[T]):
    """Generic paginated response"""
    data: List[T]
    meta: PaginationMeta

    @classmethod
    def create(cls, data: List[T], page: int, limit: int, total_items: int):
        """Create a paginated response"""
        total_pages = math.ceil(total_items / limit) if limit > 0 else 0
        has_next = page < total_pages
        has_prev = page > 1

        meta = PaginationMeta(
            page=page,
            limit=limit,
            total_items=total_items,
            total_pages=total_pages,
            has_next=has_next,
            has_prev=has_prev
        )
        return cls(data=data, meta=meta)



