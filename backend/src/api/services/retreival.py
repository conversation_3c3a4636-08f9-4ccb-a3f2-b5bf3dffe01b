from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI
from llama_index.core import VectorStoreIndex
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.query_engine import Retriever<PERSON><PERSON>yEngine
from qdrant_client import AsyncQdrantClient, QdrantClient
from dotenv import load_dotenv
from langchain_core.tools import tool
import os

load_dotenv()

# Configurable Qdrant settings from environment variables
QDRANT_HOST = os.getenv("QDRANT_HOST", "*************")
QDRANT_PORT = int(os.getenv("QDRANT_PORT", "6333"))
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY", None)
QDRANT_TIMEOUT = int(os.getenv("QDRANT_TIMEOUT", "10"))
QDRANT_HTTPS = os.getenv("QDRANT_HTTPS", "false").lower() == "true"
QDRANT_PREFER_GRPC = os.getenv("QDRANT_PREFER_GRPC", "false").lower() == "true"

sync_client = QdrantClient(
    host=QDRANT_HOST,
    port=QDRANT_PORT,
    api_key=QDRANT_API_KEY,
    timeout=QDRANT_TIMEOUT,
    https=QDRANT_HTTPS,
    prefer_grpc=QDRANT_PREFER_GRPC
)

async_client = AsyncQdrantClient(
    host=QDRANT_HOST,
    port=QDRANT_PORT,
    api_key=QDRANT_API_KEY,
    timeout=QDRANT_TIMEOUT,
    https=QDRANT_HTTPS,
    prefer_grpc=QDRANT_PREFER_GRPC
)
# Configurable collection names
QNA_COLLECTION_NAME = os.getenv("QNA_COLLECTION_NAME", "ag_salessupport_sentence_context")
PRODUCT_COLLECTION_NAME = os.getenv("PRODUCT_COLLECTION_NAME", "ag_products")

search_vector_store = QdrantVectorStore(
    client=sync_client,  # Sync client for sync operations
    aclient=async_client,  # Async client for async operations
    parallel=4,
    collection_name=QNA_COLLECTION_NAME,
    enable_hybrid=False
)

product_vector_store = QdrantVectorStore(
    client=sync_client,  # Sync client for sync operations
    aclient=async_client,  # Async client for async operations
    parallel=4,
    collection_name=PRODUCT_COLLECTION_NAME,
    enable_hybrid=False
)


search_vector_store_index = VectorStoreIndex.from_vector_store(
    vector_store=search_vector_store)

product_vector_store_index = VectorStoreIndex.from_vector_store(
    vector_store=product_vector_store)


search_retriever = VectorIndexRetriever(
    index=search_vector_store_index,
    similarity_top_k=10)
product_retriever = VectorIndexRetriever(
    index=product_vector_store_index)
# Settings.llm = OpenAI(
#                 model="gpt-4o-mini",  # Use efficient model for query processing
#                 temperature=0.1,
#                 api_key=os.getenv("OPENAI_API_KEY"),
#             )


@tool
def qna_search(query: str) -> str:
    """Search for relevant documents based on the query."""
    try:
        results = search_retriever.retrieve(query)
        response = f"Here are some relevant documents for your query '{query}': "
        if not results:
            response += "No relevant documents found."
        else:
            response += "Found the following documents:\n"
            for i, doc in enumerate(results[:3], 1):  # Limit to top 3 results
                # Assuming each doc has a 'node' attribute with the content
                # and a 'score' attribute for relevance
                node = doc.node.to_dict()
                text = node.get('text', 'Unknown Document')[:200] + "..." if len(node.get('text', '')) > 200 else node.get('text', 'Unknown Document')
                response += f"{i}. Score: {doc.score:.3f}\nContent: {text}\nMetadata: {node.get('metadata', {})}\n\n"
        return response
    except Exception as e:
        return f"Error searching documents: {str(e)}"

@tool
def search_database(query: str) -> str:
    """Search for relevant information from the database based on the query. Use this tool to find information about courses, fees, schedules, admission procedures, contact details, and customer support issues."""
    return qna_search(query)

@tool
def product_search(query: str) -> str:
    """Process the query using the LLM and return the response."""
    try:
        result = product_retriever.retrieve(query)
        response = f"Here are some products that match your query '{query}': "
        if not result:
            response += "No relevant products found."
        else:
            response += "Found the following products:\n"
            for i, doc in enumerate(result[:3], 1):  # Limit to top 3 results
                # Assuming each doc has a 'node' attribute with the content
                # and a 'score' attribute for relevance
                node = doc.node.to_dict()
                text = node.get('text', 'Unknown Product')[:200] + "..." if len(node.get('text', '')) > 200 else node.get('text', 'Unknown Product')
                response += f"{i}. Score: {doc.score:.3f}\nContent: {text}\nMetadata: {node.get('metadata', {})}\n\n"
        return response
    except Exception as e:
        return f"Error searching products: {str(e)}"


@tool
def get_today_date(input_text: str = "") -> str:
    """Get today's date. Input parameter is ignored but required for tool compatibility."""
    import datetime
    today = datetime.date.today()
    return f"Today's date is: {today}"

@tool
def create_issue_tickets(name: str, contact_number: str, description: str) -> str:
    """Create a support ticket for customer issues. Use this when customer has issues that cannot be resolved with available information."""
    try:
        import datetime
        import uuid

        # Generate ticket ID
        ticket_id = f"AG-{datetime.datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"

        # Create ticket data
        ticket_data = {
            "ticket_id": ticket_id,
            "customer_name": name,
            "contact_number": contact_number,
            "description": description,
            "status": "open",
            "priority": "medium",
            "created_at": datetime.datetime.now().isoformat(),
            "assigned_to": "counselor_team",
            "category": "customer_support"
        }

        # In a real implementation, this would save to database
        # For now, we'll simulate successful ticket creation
        print(f"Support ticket created: {ticket_data}")

        return f"✅ Support ticket {ticket_id} has been created successfully. Our counselor team will contact {name} at {contact_number} within 24 hours to resolve the issue."

    except Exception as e:
        return f"❌ Error creating support ticket: {str(e)}"


# get_today_date_tool = Tool(
#     name="get_today_date",
#     func=get_today_date,
#     description="Useful for getting today's date"
# )

# search_tool = Tool(
#     name="search",
#     func=qna_search,
#     description="Search for technical support documents, app troubleshooting guides, and educational content FAQs. Use this for questions about app issues, course access, or technical problems."
# )
# product_search_tool = Tool(
#     name="product_search",
#     func=product_search,
#     description="Search for available courses and educational products like language courses, exam preparation, etc. Use this for questions about what courses or products are available."
# )