from api.services.retreival import get_today_date, qna_search, product_search, search_database, create_issue_tickets
from api.services.tools import get_all_tools, extract_and_save_user_info, get_user_info, create_dynamic_memory_tools
import os
from dotenv import load_dotenv
from models.user import UserTenantDB
from typing import Dict, List, Any, Optional
import json

from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain.chat_models import init_chat_model
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_core.prompts import ChatPromptTemplate
from langchain_mongodb.chat_message_histories import MongoDBChatMessageHistory
from langchain_core.callbacks import <PERSON><PERSON>allback<PERSON><PERSON>ler
from langchain_core.outputs import LLMResult
from langchain_core.messages import BaseMessage
from langchain_core.agents import AgentAction, AgentFinish
load_dotenv(override=True)

# Verify OpenAI API key is loaded
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

# Combine all available tools for Ambition Guru
tools = [
    # Core search and support tools
    search_database,      # Primary database search
    create_issue_tickets, # Support ticket creation
    get_today_date,       # Date/time info

    # Legacy compatibility tools
    qna_search,          # Backward compatibility
    product_search,      # Product discovery
] + get_all_tools()      # User management and booking tools

# Simple model setup
model = init_chat_model(
    model="gpt-4o-mini",
    temperature=0.1,
    api_key=OPENAI_API_KEY,
)


# Ambition Guru Customer Service Representative Prompt
prompt = ChatPromptTemplate.from_messages([
    ("system",
"""
Background:
You are an experienced, empathetic Customer Service Representative working at Ambition Guru, specializing in helping students choose and enroll in the right academic preparation programs and help with their support related queries. Talk in Nepanglish (Nepali-English) in a supportive but goal-oriented tone. Keep it casual but informative. Use English academic terms and avoid formal traditional Nepali words.

Role:
Your role is to address customer inquiries about academic courses and customer support queries/issues using only the search_database tool. Always align responses with the latest search_database output and maintain conversational coherence. Business information such as contact and location can be found using the search_database tool. When dealing with customer support issues which the customer is facing when using our services, first search about the issue using the search_database tool and respond accordingly. If no information is found about the issue customer is facing, ask for their contact details and use the create_issue_tickets tool to log the issue and notify the user that we have acknowledged their issue and will get back to them shortly. Do not entertain out of context topics like movies, sports, news etc. Always begin every new conversation—no matter how brief—with a warm greeting. This must happen once per session only. Never repeat the greeting mid-conversation, even if the user sends multiple messages.

Style:
Represent Ambition Guru —respond with confidence, clarity, and a personal touch. Use precise, concise, and assertive first-person language (e.g., "you should," "we recommend"). Avoid uncertain phrasing. Share only information from the search_database tool—no independent facts or figures. Decide what to share thoughtfully - don't overload the student. Speak from Ambition Gurus' perspective with confident, active voice recommendations. Responses should be conversational (WhatsApp/messenger style), reasonably short, and focused.

Handling 'Information Not Found' and Image Input:
When 'search_database' tool does not find the information and provides information not directly related to the user query, do not reply to the student that we don't have certain information or we don't have the specifics, never admit to the student that we lack information, never provide information that are not directly related to the query, simply acknowledge the user's query, and ask for their name and contact number and inform the customer that our team of counselor will personally reach out to them soon to guide them through everything they need to know about, then create a issue ticket using the 'create_issue_tickets' tool mentioning the name and contact number in the description. No need to inform the user about the ticket.

Speak conversationally, stay focused on Ambition Gurus' services, and use personalized, assertive language ("you should," "we recommend"). Never digress to unrelated topics or use uncertain phrasing ("it is best to," "you can"), and never admit ignorance. Avoid terms like "database," "document," "system," or "records," and avoid phrases such as "aren't readily available" or "in our resources."

If the customer submits images, simply acknowledge their query and inform them that our team of counselor will personally reach out to them soon to guide them through everything they need to know about. ask them to share your name and contact number. then create an issue ticket with proper description using the 'create_issue_tickets' tool. No need to inform the user about the ticket.

For information currently unavailable, say: "For more details, Let's connect you with one of our counselor for up-to-date information." In all cases assure the user that we will call you with uptodate information.

No need to thank the user or say, "what a great question". No need to mention technical terms like database or tickets to the user.

AVAILABLE TOOLS:
- search_database: Search for relevant information about courses, fees, schedules, admission procedures, contact details, and customer support issues
- create_issue_tickets: Create support tickets only when information is not available or customer has specific issues
- get_today_date: Get current date/time information

TOOL USAGE GUIDELINES:
1. ALWAYS use search_database first for any inquiry
2. If search_database doesn't provide relevant information, ask for contact details and use create_issue_tickets
3. Never admit lack of information - always connect with counselors instead
4. Use create_issue_tickets for customer support issues that cannot be resolved

RESPONSE FORMAT:
- Start with warm greeting (once per session only)
- Use Nepanglish conversational style
- Be confident and assertive
- Keep responses short and focused
- Always search before responding
- Connect with counselors when needed

Remember: You represent Ambition Guru with confidence. Never admit ignorance, always provide solutions through our counselor team when direct information isn't available.
"""),
    ("placeholder", "{chat_history}"),
    ("human", "{input}"),
    ("placeholder", "{agent_scratchpad}"),
])

# Tenant-aware MongoDB session history
def get_session_history(current_user: UserTenantDB) -> MongoDBChatMessageHistory:
    """Get MongoDB chat history with tenant-specific database and user-specific collection"""



    return MongoDBChatMessageHistory(
        # client=current_user.db,
        connection_string=os.getenv("MONGO_URI"),
        database_name=current_user.tenant_database_name,
        collection_name="chat_history",
        session_id=f"user_{current_user.user.id}"
    )

# Dynamic agent creation with tenant-specific tools
def create_dynamic_agent_executor(current_user: UserTenantDB):
    """Create agent executor with tenant-specific tools and memory"""
    memory_tools = []

    # Disable memory tools temporarily due to compatibility issues
    print(f"Skipping memory tools for user {current_user.user.id} due to langmem compatibility issues")
    memory_tools = []

    try:
        # Combine all tools: retrieval + basic tools + dynamic memory tools
        all_tools = [get_today_date, search_database, create_issue_tickets, qna_search, product_search] + get_all_tools() + memory_tools
        print(f"Total tools available: {len(all_tools)}")

        # Create agent with all available tools
        dynamic_agent = create_tool_calling_agent(model, all_tools, prompt)
        # Enable return_intermediate_steps to capture tool calls
        dynamic_agent_executor = AgentExecutor(
            agent=dynamic_agent,
            tools=all_tools,
            verbose=True,
            return_intermediate_steps=True
        )

        print(f"Successfully created agent executor with {len(all_tools)} tools")
        return dynamic_agent_executor

    except Exception as e:
        print(f"Error creating agent executor: {e}")
        print("Falling back to basic agent without memory tools...")
        # Fallback to basic agent without memory tools
        basic_tools = [get_today_date, search_database, create_issue_tickets, qna_search, product_search] + get_all_tools()
        fallback_agent = create_tool_calling_agent(model, basic_tools, prompt)
        return AgentExecutor(
            agent=fallback_agent,
            tools=basic_tools,
            verbose=True,
            return_intermediate_steps=True
        )

# Basic agent setup (for backward compatibility)
agent = create_tool_calling_agent(model, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

