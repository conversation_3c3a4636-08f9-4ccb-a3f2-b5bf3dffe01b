"""
Colored logging utilities for better debugging and visibility
"""

import logging
import sys
from typing import Optional


class Colors:
    """ANSI color codes for terminal output"""
    # Basic colors
    BLACK = '\033[30m'
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    MAGENTA = '\033[35m'
    CYAN = '\033[36m'
    WHITE = '\033[37m'
    
    # Bright colors
    BRIGHT_BLACK = '\033[90m'
    BRIGHT_RED = '\033[91m'
    BRIGHT_GREEN = '\033[92m'
    BRIGHT_YELLOW = '\033[93m'
    BRIGHT_BLUE = '\033[94m'
    BRIGHT_MAGENTA = '\033[95m'
    BRIGHT_CYAN = '\033[96m'
    BRIGHT_WHITE = '\033[97m'
    
    # Styles
    BOLD = '\033[1m'
    DIM = '\033[2m'
    ITALIC = '\033[3m'
    UNDERLINE = '\033[4m'
    BLINK = '\033[5m'
    REVERSE = '\033[7m'
    STRIKETHROUGH = '\033[9m'
    
    # Reset
    RESET = '\033[0m'
    
    # Background colors
    BG_BLACK = '\033[40m'
    BG_RED = '\033[41m'
    BG_GREEN = '\033[42m'
    BG_YELLOW = '\033[43m'
    BG_BLUE = '\033[44m'
    BG_MAGENTA = '\033[45m'
    BG_CYAN = '\033[46m'
    BG_WHITE = '\033[47m'


class ColoredFormatter(logging.Formatter):
    """Custom formatter that adds colors to log levels"""
    
    COLORS = {
        'DEBUG': Colors.BRIGHT_BLACK,
        'INFO': Colors.BRIGHT_BLUE,
        'WARNING': Colors.BRIGHT_YELLOW,
        'ERROR': Colors.BRIGHT_RED,
        'CRITICAL': Colors.BRIGHT_MAGENTA + Colors.BOLD,
    }
    
    def format(self, record):
        # Get the original formatted message
        original_format = super().format(record)
        
        # Add color based on log level
        color = self.COLORS.get(record.levelname, '')
        if color:
            return f"{color}{original_format}{Colors.RESET}"
        return original_format


def setup_colored_logging(level: int = logging.INFO, format_string: Optional[str] = None):
    """
    Setup colored logging for the application
    
    Args:
        level: Logging level (default: INFO)
        format_string: Custom format string (optional)
    """
    if format_string is None:
        format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Create formatter
    formatter = ColoredFormatter(format_string)
    
    # Setup console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # Remove existing handlers to avoid duplicates
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Add our colored handler
    root_logger.addHandler(console_handler)
    
    # Reduce noise from some libraries
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('httpcore').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


def log_separator(char: str = "=", length: int = 60):
    """Print a colored separator line"""
    print(f"{Colors.BRIGHT_BLACK}{char * length}{Colors.RESET}")


def log_section_header(title: str, char: str = "=", length: int = 60):
    """Print a colored section header"""
    padding = (length - len(title) - 2) // 2
    header = f"{char * padding} {title} {char * padding}"
    if len(header) < length:
        header += char
    print(f"{Colors.BRIGHT_CYAN}{Colors.BOLD}{header}{Colors.RESET}")


def log_tool_usage(tool_name: str, input_data: str, output_data: str):
    """Log tool usage with distinctive colors"""
    print(f"{Colors.BRIGHT_MAGENTA}🔧 TOOL: {tool_name}{Colors.RESET}")
    print(f"{Colors.YELLOW}📥 INPUT: {input_data[:100]}{'...' if len(input_data) > 100 else ''}{Colors.RESET}")
    print(f"{Colors.GREEN}📤 OUTPUT: {output_data[:100]}{'...' if len(output_data) > 100 else ''}{Colors.RESET}")
    log_separator("-", 40)


def log_agent_response(agent_name: str, response: str):
    """Log agent responses with distinctive colors"""
    print(f"{Colors.BRIGHT_BLUE}🤖 {agent_name.upper()}: {Colors.RESET}")
    print(f"{Colors.WHITE}{response}{Colors.RESET}")
    log_separator("-", 40)


def log_chat_exchange(user_input: str, tools_used: list, final_response: str):
    """Log a complete chat exchange with clear formatting"""
    print(f"\n{Colors.BRIGHT_CYAN}{'='*60}{Colors.RESET}")
    print(f"{Colors.BRIGHT_CYAN}👤 USER INPUT:{Colors.RESET} {user_input}")

    if tools_used:
        print(f"\n{Colors.BRIGHT_MAGENTA}🔧 TOOLS USED:{Colors.RESET}")
        for tool in tools_used:
            tool_name = tool.get('name', 'Unknown Tool')
            tool_input = str(tool.get('input', {}))[:100]
            tool_output = str(tool.get('output', ''))[:100]
            print(f"  • {Colors.YELLOW}{tool_name}{Colors.RESET}")
            print(f"    📥 Input: {tool_input}{'...' if len(str(tool.get('input', {}))) > 100 else ''}")
            print(f"    📤 Output: {tool_output}{'...' if len(str(tool.get('output', ''))) > 100 else ''}")

    print(f"\n{Colors.BRIGHT_BLUE}🤖 FINAL RESPONSE:{Colors.RESET} {final_response}")
    print(f"{Colors.BRIGHT_CYAN}{'='*60}{Colors.RESET}\n")


def log_user_message(message: str, username: str = "User"):
    """Log user message with clean formatting"""
    print(f"{Colors.BRIGHT_CYAN}👤 {username.upper()}:{Colors.RESET} {message}")


def log_final_response(response: str):
    """Log final agent response with clean formatting"""
    print(f"{Colors.BRIGHT_BLUE}🤖 AGENT:{Colors.RESET} {response}")


def log_tool_call_clean(tool_name: str, tool_input: dict, tool_output: str):
    """Log tool call with clean, concise formatting"""
    input_str = str(tool_input)[:80] + "..." if len(str(tool_input)) > 80 else str(tool_input)
    output_str = tool_output[:80] + "..." if len(tool_output) > 80 else tool_output
    print(f"{Colors.BRIGHT_MAGENTA}🔧 {tool_name}:{Colors.RESET} {input_str} → {Colors.GREEN}{output_str}{Colors.RESET}")


def log_error(error_msg: str, exception: Optional[Exception] = None):
    """Log errors with distinctive formatting"""
    print(f"{Colors.BRIGHT_RED}❌ ERROR: {error_msg}{Colors.RESET}")
    if exception:
        print(f"{Colors.RED}Exception: {str(exception)}{Colors.RESET}")
    log_separator("-", 40)


def log_success(message: str):
    """Log success messages with distinctive formatting"""
    print(f"{Colors.BRIGHT_GREEN}✅ SUCCESS: {message}{Colors.RESET}")


def log_warning(message: str):
    """Log warning messages with distinctive formatting"""
    print(f"{Colors.BRIGHT_YELLOW}⚠️  WARNING: {message}{Colors.RESET}")


def log_info(message: str):
    """Log info messages with distinctive formatting"""
    print(f"{Colors.BRIGHT_BLUE}ℹ️  INFO: {message}{Colors.RESET}")
