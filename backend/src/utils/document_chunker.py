"""
Document Chunking Utilities
Handles breaking large documents into smaller, manageable chunks
"""

import logging
from typing import List, Dict, Any, Optional
from langchain.text_splitter import RecursiveCharacterTextSplitter, TokenTextSplitter
from langchain.schema import Document

logger = logging.getLogger(__name__)


class DocumentChunker:
    """Handles chunking of large documents into smaller pieces"""
    
    def __init__(
        self,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        separators: Optional[List[str]] = None
    ):
        """
        Initialize document chunker
        
        Args:
            chunk_size: Maximum size of each chunk
            chunk_overlap: Overlap between chunks to maintain context
            separators: Custom separators for splitting
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        
        # Default separators for better text splitting
        if separators is None:
            separators = ["\n\n", "\n", ". ", " ", ""]
        
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            separators=separators,
            length_function=len
        )
    
    def chunk_text(self, text: str, metadata: Dict[str, Any] = None) -> List[Document]:
        """
        Split text into smaller chunks
        
        Args:
            text: Text content to split
            metadata: Metadata to attach to each chunk
            
        Returns:
            List of Document objects with chunked content
        """
        try:
            if not text or not text.strip():
                return []
            
            # Split the text
            chunks = self.text_splitter.split_text(text)
            
            # Create Document objects with metadata
            documents = []
            for i, chunk in enumerate(chunks):
                chunk_metadata = metadata.copy() if metadata else {}
                chunk_metadata.update({
                    "chunk_index": i,
                    "total_chunks": len(chunks),
                    "chunk_size": len(chunk)
                })
                
                doc = Document(
                    page_content=chunk,
                    metadata=chunk_metadata
                )
                documents.append(doc)
            
            logger.info(f"Split text into {len(documents)} chunks")
            return documents
            
        except Exception as e:
            logger.error(f"Error chunking text: {e}")
            return []
    
    def chunk_documents(self, documents: List[Document]) -> List[Document]:
        """
        Chunk a list of documents
        
        Args:
            documents: List of Document objects to chunk
            
        Returns:
            List of chunked Document objects
        """
        try:
            all_chunks = []
            
            for doc_index, doc in enumerate(documents):
                # Add document index to metadata
                doc_metadata = doc.metadata.copy()
                doc_metadata["source_document_index"] = doc_index
                
                # Chunk this document
                chunks = self.chunk_text(doc.page_content, doc_metadata)
                all_chunks.extend(chunks)
            
            logger.info(f"Chunked {len(documents)} documents into {len(all_chunks)} chunks")
            return all_chunks
            
        except Exception as e:
            logger.error(f"Error chunking documents: {e}")
            return documents  # Return original if chunking fails


class TokenBasedChunker:
    """Token-based chunking for LLM processing"""
    
    def __init__(self, chunk_size: int = 500, chunk_overlap: int = 50):
        """
        Initialize token-based chunker
        
        Args:
            chunk_size: Maximum tokens per chunk
            chunk_overlap: Token overlap between chunks
        """
        self.text_splitter = TokenTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
    
    def chunk_for_llm(self, text: str, metadata: Dict[str, Any] = None) -> List[Document]:
        """
        Chunk text based on token count for LLM processing
        
        Args:
            text: Text to chunk
            metadata: Metadata for chunks
            
        Returns:
            List of Document objects optimized for LLM processing
        """
        try:
            chunks = self.text_splitter.split_text(text)
            
            documents = []
            for i, chunk in enumerate(chunks):
                chunk_metadata = metadata.copy() if metadata else {}
                chunk_metadata.update({
                    "chunk_index": i,
                    "total_chunks": len(chunks),
                    "processing_type": "llm_optimized"
                })
                
                doc = Document(
                    page_content=chunk,
                    metadata=chunk_metadata
                )
                documents.append(doc)
            
            return documents
            
        except Exception as e:
            logger.error(f"Error in token-based chunking: {e}")
            return []


def process_documents_in_batches(
    documents: List[Document],
    batch_size: int = 10,
    chunker: Optional[DocumentChunker] = None
) -> List[List[Document]]:
    """
    Process documents in smaller batches to avoid memory issues
    
    Args:
        documents: List of documents to process
        batch_size: Number of documents per batch
        chunker: Optional chunker to apply to each batch
        
    Returns:
        List of document batches
    """
    try:
        # Apply chunking if provided
        if chunker:
            documents = chunker.chunk_documents(documents)
        
        # Split into batches
        batches = []
        for i in range(0, len(documents), batch_size):
            batch = documents[i:i + batch_size]
            batches.append(batch)
        
        logger.info(f"Created {len(batches)} batches from {len(documents)} documents")
        return batches
        
    except Exception as e:
        logger.error(f"Error processing documents in batches: {e}")
        return [documents]  # Return single batch if processing fails


# Convenience functions
def create_default_chunker() -> DocumentChunker:
    """Create a default document chunker with reasonable settings"""
    return DocumentChunker(
        chunk_size=1000,
        chunk_overlap=200
    )


def create_small_chunker() -> DocumentChunker:
    """Create a chunker for smaller chunks (good for memory-constrained environments)"""
    return DocumentChunker(
        chunk_size=500,
        chunk_overlap=100
    )


def create_llm_chunker() -> TokenBasedChunker:
    """Create a token-based chunker optimized for LLM processing"""
    return TokenBasedChunker(
        chunk_size=500,
        chunk_overlap=50
    )
