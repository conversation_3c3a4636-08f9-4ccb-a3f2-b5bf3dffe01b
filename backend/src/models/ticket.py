from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from bson import ObjectId
from enum import Enum


class TicketType(str, Enum):
    """Types of tickets that can be created"""
    COURSE_BOOKING = "course_booking"
    SUPPORT_TICKET = "support_ticket"
    GENERAL_INQUIRY = "general_inquiry"
    PRODUCT_QUESTION = "product_question"
    TECHNICAL_ISSUE = "technical_issue"


class TicketStatus(str, Enum):
    """Status of tickets"""
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    PENDING_USER = "pending_user"
    RESOLVED = "resolved"
    CLOSED = "closed"
    CANCELLED = "cancelled"


class TicketPriority(str, Enum):
    """Priority levels for tickets"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class TicketModel(BaseModel):
    """Model for all types of tickets"""
    id: Optional[str] = Field(default=None, alias="_id")
    ticket_number: str  # Auto-generated unique ticket number
    ticket_type: TicketType
    
    # User Information
    user_id: str
    user_name: Optional[str] = None
    user_email: Optional[str] = None
    user_phone: Optional[str] = None
    
    # Ticket Details
    title: str
    description: str
    priority: TicketPriority = TicketPriority.MEDIUM
    status: TicketStatus = TicketStatus.OPEN
    
    # Course Booking Specific Fields (when ticket_type is COURSE_BOOKING)
    course_code: Optional[str] = None
    course_name: Optional[str] = None
    time_slot: Optional[str] = None
    
    # Additional Data
    metadata: Dict[str, Any] = {}  # For storing additional context
    tags: List[str] = []
    
    # System Fields
    tenant_id: str
    thread_id: Optional[str] = None  # Link to conversation
    created_date: datetime = Field(default_factory=datetime.now)
    updated_date: datetime = Field(default_factory=datetime.now)
    resolved_date: Optional[datetime] = None
    assigned_to: Optional[str] = None  # Agent ID if assigned
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class CourseBookingTicket(BaseModel):
    """Specialized model for course booking tickets"""
    user_name: str
    user_email: str
    user_phone: str
    course_code: str
    course_name: str
    time_slot: str
    title: str = "Course Booking Request"
    description: str = "User requested to book a course"
    priority: TicketPriority = TicketPriority.MEDIUM


class SupportTicket(BaseModel):
    """Specialized model for support tickets"""
    title: str
    description: str
    priority: TicketPriority = TicketPriority.MEDIUM
    user_name: Optional[str] = None
    user_email: Optional[str] = None
    user_phone: Optional[str] = None


class GeneralInquiryTicket(BaseModel):
    """Specialized model for general inquiry tickets"""
    title: str
    description: str
    inquiry_category: str = "general"  # general, product, service, etc.
    user_name: Optional[str] = None
    user_email: Optional[str] = None


class TicketCreate(BaseModel):
    """Model for creating tickets"""
    ticket_type: TicketType
    title: str
    description: str
    priority: TicketPriority = TicketPriority.MEDIUM
    user_name: Optional[str] = None
    user_email: Optional[str] = None
    user_phone: Optional[str] = None
    course_code: Optional[str] = None
    course_name: Optional[str] = None
    time_slot: Optional[str] = None
    metadata: Dict[str, Any] = {}
    tags: List[str] = []


class TicketUpdate(BaseModel):
    """Model for updating tickets"""
    status: Optional[TicketStatus] = None
    priority: Optional[TicketPriority] = None
    assigned_to: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None


class TicketResponse(BaseModel):
    """Model for ticket responses"""
    ticket_id: str
    ticket_number: str
    ticket_type: TicketType
    title: str
    status: TicketStatus
    priority: TicketPriority
    created_date: datetime
    user_name: Optional[str] = None
    course_name: Optional[str] = None
