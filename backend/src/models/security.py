from fastapi import Form, Request, Depends
from fastapi.security import OAuth2<PERSON>ass<PERSON>R<PERSON><PERSON><PERSON><PERSON>
from pydantic import BaseModel, <PERSON>
from typing import Optional, Union
from urllib.parse import urlparse
from helper.logger import setup_new_logging
from fastapi.exceptions import HTTPException
from core.database import get_tenant_id_and_name_from_slug
loggers = setup_new_logging(__name__)

class OAuth2PasswordRequestFormWithClientID(OAuth2PasswordRequestForm):
    def __init__(
        self,
        grant_type: str = Form(None, regex="password"),
        username: str = Form(...),
        password: str = Form(...),
        scope: str = Form(""),
        client_id: str = Form(...),  # Add client_id as a required field

    ):
        super().__init__(grant_type=grant_type, username=username, password=password, scope=scope)
        self.client_id = client_id



async def get_login_form_with_referrer_check(
    request: Request,
    form_data: OAuth2PasswordRequestFormWithClientID = Depends(),

) -> OAuth2PasswordRequestFormWithClientID:
    referrer_url = request.headers.get("referer") or request.headers.get("referrer") or request.headers.get("origin")
    hostname = None

    if referrer_url:
        parsed_referrer = urlparse(referrer_url)
        hostname = parsed_referrer.hostname

    # Fallback if no referrer or origin is found
    if not hostname:
        return form_data

    loggers.debug(f"Original client_id: {form_data.client_id}, Hostname: {hostname}")

    # If client_id is provided, respect it
    if not form_data.client_id=="eko":
        print("client_id", type(form_data.client_id))
        return form_data

    # Extract domain and subdomain
    domain_parts = hostname.split(".")
    if len(domain_parts) >= 3:
        # e.g. abc.xyz.com → subdomain = abc, domain = xyz.com
        subdomain = domain_parts[0]
        domain = ".".join(domain_parts[1:])
    elif len(domain_parts) == 2:
        subdomain = None
        domain = ".".join(domain_parts)
    else:
        return form_data  # Invalid domain

    if subdomain:
        loggers.debug(f"Identified subdomain as a slug: {subdomain}")
        a=get_tenant_id_and_name_from_slug(subdomain)
        form_data.client_id = subdomain


    return form_data


class ChangePasswordRequest(BaseModel):
    old_password: str
    new_password: str

class ResetPasswordRequest(BaseModel):
    subordinate_id: Optional[str] = None

class ExtendedTokenRequest(BaseModel):
    username: str
    password: str
    client_id: str
    days: Union[int, float] = Field(..., description="Number of days for token validity", ge=1, le=365)
