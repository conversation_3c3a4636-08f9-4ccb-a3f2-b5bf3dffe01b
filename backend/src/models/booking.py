from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field
from bson import ObjectId


class BookingModel(BaseModel):
    """Model for course bookings"""
    id: Optional[str] = Field(default=None, alias="_id")
    user_name: str
    user_email: str
    user_phone: str
    course_name: str
    course_code: str
    time_slot: str
    booking_date: datetime = Field(default_factory=datetime.now)
    status: str = "confirmed"  # confirmed, cancelled, pending
    tenant_id: str
    user_id: str
    thread_id: str  # For linking to conversation
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class BookingCreate(BaseModel):
    """Model for creating a new booking"""
    user_name: str
    user_email: str
    user_phone: str
    course_name: str
    course_code: str
    time_slot: str
    status: str = "confirmed"


class BookingResponse(BaseModel):
    """Model for booking response"""
    booking_id: str
    user_name: str
    course_name: str
    course_code: str
    time_slot: str
    booking_date: datetime
    status: str
