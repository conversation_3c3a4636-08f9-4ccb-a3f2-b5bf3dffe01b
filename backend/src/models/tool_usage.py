from datetime import datetime
from typing import Op<PERSON>, Dict, Any, List
from pydantic import BaseModel, Field
from bson import ObjectId


class ToolUsageModel(BaseModel):
    """Model for tracking tool usage in conversations"""
    id: Optional[str] = Field(default=None, alias="_id")
    tool_name: str
    tool_description: str
    input_data: Dict[str, Any] = {}
    output_data: str = ""
    execution_time_ms: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    user_id: str
    session_id: str
    tenant_id: str
    conversation_context: Optional[str] = None  # The user message that triggered this tool
    
    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}


class ToolUsageCreate(BaseModel):
    """Model for creating tool usage records"""
    tool_name: str
    tool_description: str
    input_data: Dict[str, Any] = {}
    output_data: str = ""
    execution_time_ms: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    conversation_context: Optional[str] = None


class ToolUsageResponse(BaseModel):
    """Model for tool usage API responses"""
    id: str
    tool_name: str
    tool_description: str
    input_data: Dict[str, Any]
    output_data: str
    execution_time_ms: Optional[float]
    success: bool
    error_message: Optional[str]
    timestamp: datetime
    conversation_context: Optional[str]


class ToolUsageStats(BaseModel):
    """Model for tool usage statistics"""
    total_calls: int
    successful_calls: int
    failed_calls: int
    average_execution_time: Optional[float]
    most_used_tools: List[Dict[str, Any]]
    recent_activity: List[ToolUsageResponse]


class ConversationToolUsage(BaseModel):
    """Model for tool usage in a specific conversation"""
    session_id: str
    user_id: str
    message_count: int
    tool_calls: List[ToolUsageResponse]
    first_message: datetime
    last_message: datetime
