"""
FastAPI Main Application
Multi-agent customer service system with authentication
"""

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2<PERSON>asswordBearer
from fastapi.openapi.utils import get_openapi
import uvicorn

# Import routers
from api.v1.users import router as users_router
from api.v1.chat import router as chat_router

# Setup smart logging
from core.smart_logger import log_startup, log_success

# Initialize FastAPI app
app = FastAPI(
    title="Multi-Agent Customer Service API",
    description="API for customer service with multi-agent system and authentication",
    version="2.0.0",
    swagger_ui_init_oauth={
        "clientId": "ambition-guru",
        "clientSecret": "",
        "realm": "",
        "appName": "Multi-Agent Customer Service API",
        "scopeSeparator": " ",
        "scopes": "",
        "additionalQueryStringParams": {},
        "useBasicAuthenticationWithAccessCodeGrant": False,
        "usePkceWithAuthorizationCodeGrant": False,
    }
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(users_router, prefix="/api/v1", tags=["Authentication"])
app.include_router(chat_router, prefix="/api/v1", tags=["Chat"])


@app.on_event("startup")
async def startup_event():
    """Initialize application on startup"""
    log_startup("Multi-Agent Customer Service API v2.0 starting...")
    log_startup("Swagger OAuth configured for tenant: ambition-guru")
    log_success("FastAPI application started successfully")


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Multi-Agent Customer Service API",
        "version": "2.0.0",
        "endpoints": {
            "login": "/api/v1/login",
            "chat": "/api/v1/chat",
            "chat_history": "/api/v1/history",
            "bookings": "/api/v1/bookings",
            "verify_token": "/api/v1/verify_token",
            "docs": "/docs"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "multi-agent-customer-service",
        "version": "2.0.0"
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
