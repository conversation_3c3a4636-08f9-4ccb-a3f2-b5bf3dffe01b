# Ambition Guru Customer Service Implementation

## 🎯 Overview
Successfully implemented the Ambition Guru Customer Service Representative with refined prompt and new tools as requested.

## 🔧 New Tools Added

### 1. `search_database` Tool
- **Purpose**: Primary tool for searching information about courses, fees, schedules, admission procedures, contact details, and customer support issues
- **Location**: `backend/src/api/services/retreival.py`
- **Usage**: Replaces the generic `qna_search` with more specific database search functionality

### 2. `create_issue_tickets` Tool
- **Purpose**: Create support tickets when information is not available or customer has specific issues
- **Location**: `backend/src/api/services/retreival.py`
- **Features**:
  - Generates unique ticket IDs (format: `AG-YYYYMMDD-XXXXXXXX`)
  - Stores customer name, contact number, and issue description
  - Returns confirmation message for customer
  - Assigns to counselor team automatically

## 📝 Refined Prompt Structure

### Background & Role
- **Identity**: Experienced, empathetic Customer Service Representative at Ambition Guru
- **Language**: Nepanglish (Nepali-English) conversational style
- **Tone**: Supportive but goal-oriented, casual but informative
- **Focus**: Academic preparation programs and customer support

### Key Behavioral Guidelines

#### 1. **Information Handling**
- ✅ Always use `search_database` tool first for any inquiry
- ✅ Never admit lack of information to customers
- ✅ Connect with counselors when information isn't directly available
- ❌ Never say "we don't have that information"
- ❌ Never provide unrelated information

#### 2. **Response Style**
- Use confident, assertive language ("you should," "we recommend")
- Keep responses conversational (WhatsApp/messenger style)
- Avoid uncertain phrasing ("it is best to," "you can")
- No technical terms like "database," "system," "tickets"

#### 3. **Greeting Protocol**
- Warm greeting at start of each new session (once only)
- Never repeat greetings mid-conversation

#### 4. **Issue Resolution Flow**
```
Customer Query → search_database → 
├─ Information Found → Provide confident response
└─ No Relevant Info → Ask for contact details → create_issue_tickets → Connect with counselor
```

#### 5. **Image Handling**
- Acknowledge query
- Ask for name and contact number
- Create issue ticket with proper description
- Assure counselor will reach out

## 🛠 Technical Implementation

### Files Modified
1. **`backend/src/api/services/retreival.py`**
   - Added `search_database` tool
   - Added `create_issue_tickets` tool
   - Maintained backward compatibility with `qna_search`

2. **`backend/src/api/services/test.py`**
   - Updated imports to include new tools
   - Completely replaced prompt with Ambition Guru specification
   - Updated tool lists in agent creation functions

### Tool Integration
```python
# New tools added to agent
tools = [
    get_today_date,
    search_database,      # Primary search tool
    create_issue_tickets, # Support ticket creation
    qna_search,          # Backward compatibility
    product_search       # Product discovery
] + get_all_tools()
```

## 🎯 Key Features Implemented

### ✅ **Customer Service Excellence**
- Never admits ignorance
- Always provides solutions through counselor connection
- Confident, professional responses
- Nepanglish conversational style

### ✅ **Smart Issue Resolution**
- Automatic ticket creation for unresolved queries
- Seamless handoff to counselor team
- Customer contact information capture
- Professional follow-up assurance

### ✅ **Tool Usage Guidelines**
- `search_database`: Primary information retrieval
- `create_issue_tickets`: When search doesn't yield relevant results
- Proper error handling and user experience

### ✅ **Response Format**
- Warm greeting (once per session)
- Confident, assertive language
- Short, focused responses
- No technical jargon exposed to customers

## 🧪 Testing
Created `backend/test_ambition_guru.py` to verify:
- Tool imports work correctly
- Ticket creation functionality
- Prompt structure contains required elements
- Ambition Guru branding is present

## 🚀 Usage Examples

### Successful Information Retrieval
```
Customer: "IELTS course ko fee kati ho?"
Agent: search_database("IELTS course fee") → Provides confident response with pricing
```

### Information Not Available
```
Customer: "New course launch kaha huncha?"
Agent: search_database("new course launch") → No relevant info
Agent: "For more details, let's connect you with one of our counselors. Can you share your name and contact number?"
Customer: Provides details
Agent: create_issue_tickets(name, contact, description) → "Our counselor will call you with up-to-date information"
```

## 📋 Next Steps
1. Test the implementation with real customer queries
2. Monitor ticket creation and counselor follow-up process
3. Refine responses based on customer feedback
4. Add more specific course and program information to database

---
**Status**: ✅ Implementation Complete and Ready for Testing
