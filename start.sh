#!/bin/bash

# Multi-tenant Chatbot System Startup Script
echo "🚀 Starting Multi-tenant Chatbot System..."

# Function to kill background processes on exit
cleanup() {
    echo "🛑 Shutting down servers..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    exit
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start Backend (FastAPI)
echo "🔧 Starting Backend Server..."
cd backend
uv run python src/main.py &
BACKEND_PID=$!
cd ..


# Start Frontend (React/Vite)
echo "🎨 Starting Frontend Server..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo "✅ Both servers started successfully!"
echo ""
echo "📋 Server Information:"
echo "   🔧 Backend (FastAPI): http://localhost:8000"
echo "   🎨 Frontend (React):  http://localhost:5173"
echo "   📚 API Docs:          http://localhost:8000/docs"
echo ""
echo "🔑 Test Credentials:"
echo "   Client ID: ambition-guru"
echo "   Admin:     admin / admin123"
echo "   Supervisor: supervisor / supervisor123"
echo "   Agent:     agent1 / agent123"
echo ""
echo "Press Ctrl+C to stop both servers"

# Wait for background processes
wait $BACKEND_PID $FRONTEND_PID
