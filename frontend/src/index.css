@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for chat interface */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.6s ease-out;
}

.animate-slideInUp {
  animation: slideInUp 0.5s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.4s ease-out;
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200px 100%;
}

/* Chat Layout within Protected Layout */
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  scroll-behavior: smooth;
}

/* Smooth scrolling for messages */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Ensure proper layout for chat container */
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Messages area should be scrollable and fill remaining space */
.messages-area {
  flex: 1;
  overflow-y: auto;
  min-height: 0; /* Important for flex child to be scrollable */
}

/* Input area should be fixed at bottom */
.input-area {
  flex-shrink: 0;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* Custom scrollbar for chat area */
.chat-scroll::-webkit-scrollbar {
  width: 6px;
}

.chat-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.chat-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.chat-scroll::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Prose styling for formatted AI messages */
.prose {
  max-width: none;
}

.prose p {
  margin-bottom: 0.5rem;
}

.prose ul {
  margin: 0.5rem 0;
  padding-left: 1rem;
}

.prose li {
  margin-bottom: 0.25rem;
}

@layer components {
  /* Custom component styles */
  .chat-bubble {
    @apply max-w-xs lg:max-w-md px-4 py-2 rounded-2xl relative group;
  }

  .chat-bubble-user {
    @apply bg-blue-600 text-white rounded-br-md;
  }

  .chat-bubble-bot {
    @apply bg-white border border-gray-200 rounded-bl-md;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent;
  }
}

/* Base styles */
* {
  border-color: #e5e7eb;
}

body {
  background-color: white;
  color: #111827;
  font-feature-settings: "rlig" 1, "calt" 1;
  margin: 0;
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
}

@layer components {
  /* Custom component styles */
  .chat-bubble {
    @apply max-w-xs lg:max-w-md px-4 py-2 rounded-2xl relative group;
  }

  .chat-bubble-user {
    @apply bg-blue-600 text-white rounded-br-md;
  }

  .chat-bubble-bot {
    @apply bg-white text-gray-800 shadow-md border rounded-bl-md;
  }
}

@layer utilities {
  /* Custom utility classes */
  .text-balance {
    text-wrap: balance;
  }

  .animate-typing {
    animation: typing 1.5s infinite;
  }

  @keyframes typing {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
  }

  .animate-slideInUp {
    animation: slideInUp 0.5s ease-out;
  }

  .animate-slideInRight {
    animation: slideInRight 0.5s ease-out;
  }

  .animate-slideInLeft {
    animation: slideInLeft 0.5s ease-out;
  }

  .animate-fadeIn {
    animation: fadeIn 0.5s ease-out;
  }

  @keyframes slideInUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideInRight {
    from {
      transform: translateX(20px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideInLeft {
    from {
      transform: translateX(-20px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  .animate-slideUp {
    animation: slideUp 0.6s ease-out;
  }

  /* Enhanced chat scroll styling */
  .chat-scroll {
    scrollbar-width: thin;
    scrollbar-color: rgba(147, 51, 234, 0.3) transparent;
    scroll-behavior: smooth;
    overflow-anchor: none;
  }

  .chat-scroll::-webkit-scrollbar {
    width: 6px;
  }

  .chat-scroll::-webkit-scrollbar-track {
    background: transparent;
  }

  .chat-scroll::-webkit-scrollbar-thumb {
    background: rgba(147, 51, 234, 0.3);
    border-radius: 3px;
  }

  .chat-scroll::-webkit-scrollbar-thumb:hover {
    background: rgba(147, 51, 234, 0.5);
  }
}



@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 240 10% 8%;
    --foreground: 0 0% 95%;
    --card: 240 10% 10%;
    --card-foreground: 0 0% 95%;
    --popover: 240 10% 10%;
    --popover-foreground: 0 0% 95%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 240 10% 8%;
    --secondary: 240 4% 16%;
    --secondary-foreground: 0 0% 95%;
    --muted: 240 4% 16%;
    --muted-foreground: 240 5% 65%;
    --accent: 240 4% 16%;
    --accent-foreground: 0 0% 95%;
    --destructive: 0 62.8% 50%;
    --destructive-foreground: 0 0% 95%;
    --border: 240 4% 20%;
    --input: 240 4% 16%;
    --ring: 217.2 91.2% 59.8%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 10% 8%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 240 10% 8%;
    --sidebar-accent: 240 4% 16%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 240 4% 20%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
