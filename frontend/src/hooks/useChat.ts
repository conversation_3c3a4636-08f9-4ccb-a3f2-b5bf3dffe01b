/**
 * Chat Hook
 * Manages chat state and provides chat functionality
 */

import { useState, useCallback, useEffect } from 'react';
import { chatService } from '../services';
import type { ChatMessage, ChatHistoryMessage } from '../services';

interface UseChatReturn {
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  sendMessage: (message: string, useStreaming?: boolean) => Promise<void>;
  clearHistory: () => Promise<void>;
  refreshHistory: () => Promise<void>;
  loadMoreHistory: () => Promise<void>;
  isTyping: boolean;
  streamingResponse: string;
  hasMoreHistory: boolean;
  isLoadingMore: boolean;
  selectedMessage: string | null;
  setSelectedMessage: (id: string | null) => void;
  userTyping: boolean;
  setUserTyping: (typing: boolean) => void;
  assistantTyping: boolean;
  pendingUserMessage: string | null;
}

export const useChat = (): UseChatReturn => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [streamingResponse, setStreamingResponse] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreHistory, setHasMoreHistory] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<string | null>(null);
  const [userTyping, setUserTyping] = useState(false);
  const [assistantTyping, setAssistantTyping] = useState(false);
  const [pendingUserMessage, setPendingUserMessage] = useState<string | null>(null);

  // Load chat history from server on mount
  useEffect(() => {
    loadChatHistory();
  }, []);

  const loadChatHistory = async (page: number = 1, append: boolean = false) => {
    try {
      if (!append) setIsLoading(true);
      else setIsLoadingMore(true);

      const serverHistory = await chatService.getServerChatHistory(page, 5);

      // Convert server history to local format
      const convertedMessages: ChatMessage[] = [];
      for (let i = 0; i < serverHistory.data.length; i += 2) {
        const humanMsg = serverHistory.data[i];
        const aiMsg = serverHistory.data[i + 1];

        if (humanMsg && humanMsg.type === 'human') {
          convertedMessages.push({
            id: `${Date.now()}-${i}-${page}`,
            message: humanMsg.content,
            response: aiMsg?.content || '',
            timestamp: humanMsg.timestamp || new Date(),
            thread_id: 'current_session',
            user_id: 'current_user',
            tools_used: aiMsg?.tools_used || [] // Extract tool calls from AI message
          });
        }
      }

      // Update pagination state
      setHasMoreHistory(serverHistory.meta.has_next);

      if (append) {
        setMessages(prev => [...convertedMessages, ...prev]);
      } else {
        setMessages(convertedMessages);
      }
    } catch (err) {
      console.warn('Failed to load server chat history, falling back to local:', err);
      if (!append) {
        // Fallback to local storage only for initial load
        const localHistory = chatService.getChatHistory();
        setMessages(localHistory);
      }
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  };

  const refreshHistory = useCallback(async (): Promise<void> => {
    setCurrentPage(1);
    await loadChatHistory(1, false);
  }, []);

  const loadMoreHistory = useCallback(async (): Promise<void> => {
    if (hasMoreHistory && !isLoadingMore) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      await loadChatHistory(nextPage, true);
    }
  }, [currentPage, hasMoreHistory, isLoadingMore]);

  const sendMessage = useCallback(async (message: string, useStreaming: boolean = false): Promise<void> => {
    if (!message.trim()) return;

    setIsLoading(true);
    setError(null);
    setStreamingResponse('');
    setUserTyping(false); // Stop user typing animation

    // Store the user message temporarily - don't add to messages yet
    const tempUserMessage = {
      id: Date.now().toString(),
      message,
      response: '',
      timestamp: new Date(),
      thread_id: '',
      user_id: '',
      tools_used: [],
    };

    // After slight delay, start assistant typing
    setTimeout(() => {
      setAssistantTyping(true);
      setIsTyping(true);
    }, 500); // 500ms delay before assistant starts typing

    try {

      if (useStreaming) {
        // Use streaming API
        await chatService.sendMessageStream(
          message,
          (chunk) => {
            // Handle streaming chunks
            if (chunk.type === 'chunk') {
              setStreamingResponse(prev => prev + (chunk.content || ''));
            }
          },
          (response) => {
            // Handle completion - add the complete message with response
            const completeMessage: ChatMessage = {
              ...tempUserMessage,
              response: response.response,
              thread_id: response.thread_id,
              user_id: response.user_id,
              tools_used: response.tools_used || [],
            };

            setMessages(prev => [...prev, completeMessage]);
            setStreamingResponse('');
            setAssistantTyping(false);
          },
          (error) => {
            // Handle streaming error
            setError(error);
            setStreamingResponse('');
            setAssistantTyping(false);
          }
        );
      } else {
        // Use regular API
        const response = await chatService.sendMessage(message);

        // Add the complete message with response
        const completeMessage: ChatMessage = {
          ...tempUserMessage,
          response: response.response,
          thread_id: response.thread_id,
          user_id: response.user_id,
          tools_used: response.tools_used || [],
        };

        setMessages(prev => [...prev, completeMessage]);
        setAssistantTyping(false);
      }
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'Failed to send message');
      setStreamingResponse('');
      setAssistantTyping(false);
      setPendingUserMessage(null);
      // No need to remove message since we didn't add it yet
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  }, []);

  const clearHistory = useCallback(async (): Promise<void> => {
    try {
      // Clear both server and local history
      await chatService.clearServerChatHistory();
      await chatService.clearHistory(); // Also clear local storage
      setMessages([]);
      setError(null);
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'Failed to clear conversation');
    }
  }, []);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    clearHistory,
    refreshHistory,
    loadMoreHistory,
    isTyping,
    streamingResponse,
    hasMoreHistory,
    isLoadingMore,
    selectedMessage,
    setSelectedMessage,
    userTyping,
    setUserTyping,
    assistantTyping,
    pendingUserMessage,
  };
};
