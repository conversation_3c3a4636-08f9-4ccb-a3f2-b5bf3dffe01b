/**
 * Main App Component
 * Handles routing and authentication
 */

import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './hooks/useAuth';
import { Loading } from './components';
import { ProtectedLayout, SuperProtectedLayout } from './layouts';
import { ThemeProvider } from './contexts/ThemeContext';
import AuthRouter from './pages/auth/auth.index';
import Dashboard from './pages/dashboard/Dashboard';
import SimplifiedChat from './pages/chat/SimplifiedChat';

// Protected Route Component
const ProtectedRoute: React.FC<{ children: React.ReactNode; requireAdmin?: boolean }> = ({
  children,
  requireAdmin = false
}) => {
  const { isAuthenticated, isLoading, user } = useAuth();

  if (isLoading) {
    return <Loading fullScreen text="Checking authentication..." />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/auth/login" replace />;
  }

  if (requireAdmin && user?.role !== 'admin') {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

function App() {
  const { user } = useAuth();

  // Determine which layout to use based on user role
  const getLayoutComponent = () => {
    if (user?.role === 'admin') {
      return SuperProtectedLayout;
    }
    return ProtectedLayout;
  };

  const LayoutComponent = getLayoutComponent();

  return (
    <ThemeProvider>
      <Router>
        <div className="App">
          <Routes>
          {/* Auth Routes */}
          <Route path="/auth/*" element={<AuthRouter />} />

          {/* Protected Routes - Normal Users */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <LayoutComponent>
                  <Dashboard />
                </LayoutComponent>
              </ProtectedRoute>
            }
          />

          <Route
            path="/playground"
            element={
              <ProtectedRoute>
                <LayoutComponent>
                  <SimplifiedChat />
                </LayoutComponent>
              </ProtectedRoute>
            }
          />

          <Route
            path="/profile"
            element={
              <ProtectedRoute>
                <LayoutComponent>
                  <div className="p-6">
                    <h1 className="text-2xl font-bold">Profile</h1>
                    <p>Profile page coming soon...</p>
                  </div>
                </LayoutComponent>
              </ProtectedRoute>
            }
          />

          <Route
            path="/settings"
            element={
              <ProtectedRoute>
                <LayoutComponent>
                  <div className="p-6">
                    <h1 className="text-2xl font-bold">Settings</h1>
                    <p>Settings page coming soon...</p>
                  </div>
                </LayoutComponent>
              </ProtectedRoute>
            }
          />

          {/* Admin Routes */}
          <Route
            path="/admin/users"
            element={
              <ProtectedRoute requireAdmin>
                <SuperProtectedLayout>
                  <div className="p-6">
                    <h1 className="text-2xl font-bold">User Management</h1>
                    <p>User management interface coming soon...</p>
                  </div>
                </SuperProtectedLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/settings"
            element={
              <ProtectedRoute requireAdmin>
                <SuperProtectedLayout>
                  <div className="p-6">
                    <h1 className="text-2xl font-bold">System Settings</h1>
                    <p>System settings interface coming soon...</p>
                  </div>
                </SuperProtectedLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/security"
            element={
              <ProtectedRoute requireAdmin>
                <SuperProtectedLayout>
                  <div className="p-6">
                    <h1 className="text-2xl font-bold">Security</h1>
                    <p>Security management interface coming soon...</p>
                  </div>
                </SuperProtectedLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/admin/database"
            element={
              <ProtectedRoute requireAdmin>
                <SuperProtectedLayout>
                  <div className="p-6">
                    <h1 className="text-2xl font-bold">Database Management</h1>
                    <p>Database management interface coming soon...</p>
                  </div>
                </SuperProtectedLayout>
              </ProtectedRoute>
            }
          />

          <Route
            path="/analytics"
            element={
              <ProtectedRoute>
                <LayoutComponent>
                  <div className="p-6">
                    <h1 className="text-2xl font-bold">Analytics</h1>
                    <p>Analytics dashboard coming soon...</p>
                  </div>
                </LayoutComponent>
              </ProtectedRoute>
            }
          />

          {/* Default Route */}
          <Route path="/" element={<Navigate to="/dashboard" replace />} />

          {/* Catch All */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </div>
      </Router>
    </ThemeProvider>
  );
}

export default App;
