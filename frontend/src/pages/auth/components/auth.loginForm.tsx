/**
 * Login Form Component
 * Presentational component for login form UI
 */

import React from 'react';
import { motion } from 'framer-motion';
import { User, Lock, Building } from 'lucide-react';
import { UIButton as Button, UIInput as Input, Label } from '@/components';
import PublicLayout from '@/layouts/PublicLayout';

interface LoginFormProps {
  username: string;
  password: string;
  clientId: string;
  isLoading: boolean;
  error: string | null;
  onUsernameChange: (value: string) => void;
  onPasswordChange: (value: string) => void;
  onClientIdChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  onSignupClick: () => void;
}

const LoginForm: React.FC<LoginFormProps> = ({
  username,
  password,
  clientId,
  isLoading,
  error,
  onUsernameChange,
  onPasswordChange,
  onClientIdChange,
  onSubmit,
  onSignupClick,
}) => {
  return (
    <PublicLayout title="Welcome back" subtitle="Sign in to your account to continue">

      {error && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg"
        >
          <p className="text-sm text-red-600">{error}</p>
        </motion.div>
      )}

      <form onSubmit={onSubmit} className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="username">Username</Label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="username"
                type="text"
                value={username}
                onChange={(e) => onUsernameChange(e.target.value)}
                placeholder="Enter your username"
                className="pl-10"
                required
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => onPasswordChange(e.target.value)}
                placeholder="Enter your password"
                className="pl-10"
                required
                disabled={isLoading}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="clientId">Client ID</Label>
            <div className="relative">
              <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="clientId"
                type="text"
                value={clientId}
                onChange={(e) => onClientIdChange(e.target.value)}
                placeholder="ambition-guru"
                className="pl-10"
                disabled={isLoading}
              />
            </div>
            <p className="text-xs text-gray-500">Default: ambition-guru</p>
          </div>
        </div>

        <div className="space-y-4">
          <Button
            type="submit"
            className="w-full"
            disabled={isLoading}
          >
            {isLoading ? 'Signing in...' : 'Sign In'}
          </Button>

          <div className="text-center">
            <span className="text-sm text-gray-600">
              Don't have an account?{' '}
              <button
                type="button"
                onClick={onSignupClick}
                className="font-medium text-blue-600 hover:text-blue-500 transition-colors"
                disabled={isLoading}
              >
                Sign up
              </button>
            </span>
          </div>
        </div>
      </form>
    </PublicLayout>
  );
};

export default LoginForm;
