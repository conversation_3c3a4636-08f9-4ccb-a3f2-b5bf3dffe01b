/**
 * Dashboard Page
 * Main dashboard with dummy data and statistics
 */

import React from 'react';
import { motion } from 'framer-motion';
import {
  BookOpen,
  Users,
  TrendingUp,
  Calendar,
  MessageCircle,
  Award,
  Clock,
  Target,
  ArrowUpRight,
  Activity,
  BarChart3
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  trend?: string;
  trendDirection?: 'up' | 'down';
  description?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  trend,
  trendDirection = 'up',
  description
}) => (
  <motion.div
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
  >
    <Card className="relative overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="h-4 w-4 text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {trend && (
          <div className={`flex items-center text-xs ${
            trendDirection === 'up' ? 'text-green-600' : 'text-red-600'
          }`}>
            <ArrowUpRight className={`h-3 w-3 mr-1 ${
              trendDirection === 'down' ? 'rotate-90' : ''
            }`} />
            {trend}
          </div>
        )}
        {description && (
          <p className="text-xs text-muted-foreground mt-1">
            {description}
          </p>
        )}
      </CardContent>
    </Card>
  </motion.div>
);

const Dashboard: React.FC = () => {
  const { user } = useAuth();

  const stats = [
    {
      title: 'Total Courses',
      value: '13',
      icon: <BookOpen className="h-4 w-4" />,
      trend: '+2 this month',
      description: 'Active learning modules'
    },
    {
      title: 'Active Students',
      value: '1,247',
      icon: <Users className="h-4 w-4" />,
      trend: '+15% this week',
      description: 'Enrolled learners'
    },
    {
      title: 'Completion Rate',
      value: '87%',
      icon: <Award className="h-4 w-4" />,
      trend: '+5% improvement',
      description: 'Course completion'
    },
    {
      title: 'Chat Sessions',
      value: '342',
      icon: <MessageCircle className="h-4 w-4" />,
      trend: '+23 today',
      description: 'AI interactions'
    }
  ];

  const recentCourses = [
    { name: 'IELTS Preparation', students: 89, status: 'Active' },
    { name: 'Korean Language - TOPIK Level 1', students: 67, status: 'Active' },
    { name: 'SEE Bridge Course', students: 156, status: 'Active' },
    { name: 'German Language - A1 Level', students: 43, status: 'Active' },
    { name: 'CSIT Entrance Prep', students: 78, status: 'Active' }
  ];

  const recentActivities = [
    { action: 'New student enrolled in IELTS Preparation', time: '2 minutes ago' },
    { action: 'Chat session completed for Korean course inquiry', time: '15 minutes ago' },
    { action: 'Course completion certificate issued', time: '1 hour ago' },
    { action: 'New booking for German A1 Level', time: '2 hours ago' },
    { action: 'Student feedback received for SEE Bridge', time: '3 hours ago' }
  ];

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Welcome back, {user?.username || 'User'}!
          </h1>
          <p className="text-muted-foreground">
            Here's what's happening with your courses today.
          </p>
        </div>
        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
          <Calendar className="h-4 w-4" />
          <span>{new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })}</span>
        </div>
      </motion.div>
      {/* Stats Grid */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="grid gap-4 md:grid-cols-2 lg:grid-cols-4"
      >
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 + index * 0.1 }}
          >
            <StatCard {...stat} />
          </motion.div>
        ))}
      </motion.div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
        {/* Recent Courses */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="col-span-4"
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Popular Courses
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardTitle>
              <CardDescription>
                Most engaged courses this week
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentCourses.map((course, index) => (
                <motion.div
                  key={course.name}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 + index * 0.1 }}
                  className="flex items-center justify-between p-3 rounded-lg border hover:bg-accent transition-colors cursor-pointer"
                >
                  <div>
                    <p className="font-medium">{course.name}</p>
                    <p className="text-sm text-muted-foreground">{course.students} students</p>
                  </div>
                  <Badge variant="secondary">
                    {course.status}
                  </Badge>
                </motion.div>
              ))}
            </CardContent>
          </Card>
        </motion.div>

        {/* Recent Activities */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="col-span-3"
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Recent Activities
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardTitle>
              <CardDescription>
                Latest system activities
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentActivities.map((activity, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 + index * 0.1 }}
                  className="flex items-start space-x-3 p-3 hover:bg-accent rounded-lg transition-colors"
                >
                  <div className="flex-shrink-0 w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">{activity.action}</p>
                    <p className="text-xs text-muted-foreground">{activity.time}</p>
                  </div>
                </motion.div>
              ))}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default Dashboard;
