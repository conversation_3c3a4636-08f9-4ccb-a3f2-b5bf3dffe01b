/**
 * Chat Page - Playground Interface
 * Modern chat interface with code display panel
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  Trash2,
  Code2,
  <PERSON><PERSON>,
  ChevronR<PERSON>,
  <PERSON>fresh<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>r,
  <PERSON>rkles,
  Zap,
  Wifi,
  WifiOff,
  <PERSON>ch,
  Play,
  CheckCircle,
  AlertCircle,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useChat } from '../../hooks/useChat';
import ChatInput from './components/ChatInput';
import { Button } from '../../components';
import ConfirmDialog from '../../components/ConfirmDialog';
import type { ToolUsed } from '../../services';

// Tool Call Display Component
interface ToolCallDisplayProps {
  tools: ToolUsed[];
  isExpanded: boolean;
  onToggle: () => void;
}

const ToolCallDisplay: React.FC<ToolCallDisplayProps> = ({ tools, isExpanded, onToggle }) => {
  if (!tools || tools.length === 0) return null;

  return (
    <div className="mt-3 border border-blue-200 rounded-lg bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800">
      <button
        onClick={onToggle}
        className="w-full px-3 py-2 flex items-center justify-between text-sm font-medium text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors rounded-lg"
      >
        <div className="flex items-center gap-2">
          <Wrench className="w-4 h-4" />
          <span>{tools.length} tool{tools.length > 1 ? 's' : ''} used</span>
        </div>
        {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
      </button>

      {isExpanded && (
        <div className="px-3 pb-3 space-y-2 animate-in slide-in-from-top-2 duration-200">
          {tools.map((tool, index) => (
            <div key={index} className="bg-white dark:bg-gray-800 rounded-md p-3 border border-blue-200 dark:border-blue-700">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="font-medium text-sm text-gray-900 dark:text-gray-100">{tool.name}</span>
              </div>

              {tool.description && (
                <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">{tool.description}</p>
              )}

              {tool.input && Object.keys(tool.input).length > 0 && (
                <div className="mb-2">
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Input:</span>
                  <pre className="text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded mt-1 overflow-x-auto">
                    {JSON.stringify(tool.input, null, 2)}
                  </pre>
                </div>
              )}

              {tool.output && (
                <div>
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Output:</span>
                  <div className="text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded mt-1 max-h-32 overflow-y-auto">
                    {typeof tool.output === 'string' ? tool.output : JSON.stringify(tool.output, null, 2)}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const Chat: React.FC = () => {
  const {
    messages,
    isLoading,
    error,
    sendMessage,
    clearHistory,
    refreshHistory,
    loadMoreHistory,
    isTyping,
    streamingResponse,
    hasMoreHistory,
    isLoadingMore
  } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [selectedMessage, setSelectedMessage] = useState<string | null>(null);
  const [showCodePanel, setShowCodePanel] = useState(false); // Start hidden
  const [useStreaming, setUseStreaming] = useState(false); // Default to normal chat (non-streaming)
  const [userTyping, setUserTyping] = useState(false); // Track user typing
  const [sendingMessage, setSendingMessage] = useState<string | null>(null); // Track message being sent
  const [showSendAnimation, setShowSendAnimation] = useState(false); // Control send animation
  const [showClearDialog, setShowClearDialog] = useState(false); // Control clear confirmation dialog
  const [isOnline, setIsOnline] = useState(navigator.onLine); // Track online status
  const [expandedTools, setExpandedTools] = useState<Set<string>>(new Set()); // Track expanded tool displays

  // Helper function to format timestamp
  const formatTimestamp = (timestamp: Date | string) => {
    const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Format AI message content with better styling
  const formatAIMessage = (content: string) => {
    // Split content into lines and format
    const lines = content.split('\n');
    const formattedContent: React.ReactElement[] = [];

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();

      if (trimmedLine === '') {
        formattedContent.push(<br key={`br-${index}`} />);
      } else if (trimmedLine.match(/^\d+\.\s+\*\*.*\*\*/)) {
        // Numbered list with bold items (e.g., "1. **SEE Bridge Course**")
        const match = trimmedLine.match(/^(\d+)\.\s+\*\*(.*?)\*\*(.*)$/);
        if (match) {
          formattedContent.push(
            <div key={index} className="flex items-start space-x-3 mb-3">
              <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                {match[1]}
              </div>
              <div>
                <span className="font-semibold text-gray-900">{match[2]}</span>
                <span className="text-gray-700">{match[3]}</span>
              </div>
            </div>
          );
        }
      } else if (trimmedLine.startsWith('**') && trimmedLine.endsWith('**')) {
        // Bold headers
        const text = trimmedLine.slice(2, -2);
        formattedContent.push(
          <div key={index} className="font-bold text-gray-900 mb-2 flex items-center">
            <Sparkles className="h-4 w-4 text-purple-500 mr-2" />
            {text}
          </div>
        );
      } else if (trimmedLine.includes('**')) {
        // Inline bold text
        const parts = trimmedLine.split(/\*\*(.*?)\*\*/g);
        formattedContent.push(
          <div key={index} className="mb-2">
            {parts.map((part, partIndex) =>
              partIndex % 2 === 1 ? (
                <span key={partIndex} className="font-semibold text-gray-900">{part}</span>
              ) : (
                <span key={partIndex} className="text-gray-700">{part}</span>
              )
            )}
          </div>
        );
      } else {
        // Regular text
        formattedContent.push(
          <div key={index} className="text-gray-700 mb-1">{trimmedLine}</div>
        );
      }
    });

    return <div className="space-y-1">{formattedContent}</div>;
  };

  // Check if any message has tool calls (for showing the toggle button)
  const hasAnyToolCalls = messages.some(msg => msg.tools_used && msg.tools_used.length > 0);

  // Get the selected message and check if it has tool calls
  const selectedMsg = messages.find(msg => msg.id === selectedMessage);
  const selectedHasToolCalls = selectedMsg?.tools_used && selectedMsg.tools_used.length > 0;

  // Auto-show tools panel when a message with tool calls is selected
  useEffect(() => {
    if (selectedMessage && selectedHasToolCalls) {
      setShowCodePanel(true);
    } else if (!selectedMessage) {
      setShowCodePanel(false);
    }
  }, [selectedMessage, selectedHasToolCalls]);

  // Auto-scroll to bottom when new messages arrive (but not when loading more history)
  useEffect(() => {
    if (!isLoadingMore) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isTyping, isLoadingMore]);

  // Maintain scroll position when loading more history
  useEffect(() => {
    if (isLoadingMore && messagesContainerRef.current) {
      const container = messagesContainerRef.current;
      const scrollHeight = container.scrollHeight;

      // After loading more, adjust scroll position to maintain view
      setTimeout(() => {
        if (container.scrollHeight > scrollHeight) {
          container.scrollTop = container.scrollHeight - scrollHeight;
        }
      }, 100);
    }
  }, [messages.length, isLoadingMore]);

  const handleSendMessage = async (message: string) => {
    setUserTyping(false); // Stop user typing animation
    setSendingMessage(message); // Set the message being sent
    setShowSendAnimation(true); // Show send animation

    // Brief delay to show the send animation
    setTimeout(() => {
      setShowSendAnimation(false);
      setSendingMessage(null);
    }, 300); // Reduced to 300ms for faster response

    // Send message (the useChat hook will handle the assistant typing delay)
    await sendMessage(message, useStreaming);
  };

  const handleClearHistory = async () => {
    setShowClearDialog(true);
  };

  const confirmClearHistory = async () => {
    try {
      await clearHistory();
    } catch (error) {
      console.error('Failed to clear history:', error);
      // Error is already handled in useChat hook
    }
  };



  // Get tools used for a specific message
  const getToolsForMessage = (messageId: string) => {
    const message = messages.find(msg => msg.id === messageId);
    if (!message || !message.tools_used) {
      return [];
    }

    return message.tools_used.map(tool => ({
      name: tool.name,
      description: tool.description || `Tool: ${tool.name}`,
      input: tool.input || {},
      output: tool.output || 'Tool executed successfully',
      code: `# Tool: ${tool.name}
# Description: ${tool.description || `Execute ${tool.name} tool`}
# Input: ${JSON.stringify(tool.input, null, 2)}
# Output: ${tool.output || 'Tool executed successfully'}

def ${tool.name}(${Object.keys(tool.input || {}).join(', ')}):
    """${tool.description || `Execute ${tool.name} tool`}"""
    # Tool implementation would be here
    return result`,
      language: "python"
    }));
  };

  return (
    <div className="h-full flex bg-gray-50">
      {/* Chat Panel */}
      <div className="flex-1 flex flex-col bg-white">
        {/* Enhanced Chat Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-white to-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                  <Bot className="h-6 w-6 text-white" />
                </div>
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  EduMind AI
                </h1>
                <p className="text-sm text-gray-600 font-medium">
                  Your intelligent learning companion
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* Streaming Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setUseStreaming(!useStreaming)}
                title={useStreaming ? "Switch to Normal Chat" : "Switch to Streaming Chat"}
                className={`transition-colors ${
                  useStreaming
                    ? 'bg-green-100 text-green-700 hover:bg-green-200'
                    : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                }`}
              >
                {useStreaming ? (
                  <>
                    <Wifi className="h-4 w-4 mr-1" />
                    <span className="text-xs font-medium">Streaming</span>
                  </>
                ) : (
                  <>
                    <WifiOff className="h-4 w-4 mr-1" />
                    <span className="text-xs font-medium">Normal</span>
                  </>
                )}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={refreshHistory}
                disabled={isLoading}
                title="Refresh chat history from server"
                className="hover:bg-purple-100 hover:text-purple-700 transition-colors"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearHistory}
                disabled={messages.length === 0}
                className="hover:bg-red-100 hover:text-red-700 transition-colors"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              {hasAnyToolCalls && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCodePanel(!showCodePanel)}
                  className={`transition-colors ${
                    showCodePanel
                      ? 'bg-purple-100 text-purple-700'
                      : 'hover:bg-purple-100 hover:text-purple-700'
                  }`}
                >
                  <Code2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Chat Messages */}
        <div
          ref={messagesContainerRef}
          className="flex-1 overflow-y-auto px-6 py-6 space-y-8 chat-scroll"
          onScroll={(e) => {
            const container = e.currentTarget;
            // Load more when scrolled to top
            if (container.scrollTop === 0 && hasMoreHistory && !isLoadingMore) {
              loadMoreHistory();
            }
          }}
        >
          {/* Load More Button */}
          {hasMoreHistory && (
            <div className="text-center py-4">
              <button
                onClick={loadMoreHistory}
                disabled={isLoadingMore}
                className="px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoadingMore ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    Loading more...
                  </div>
                ) : (
                  'Load more messages'
                )}
              </button>
            </div>
          )}

          {messages.length === 0 ? (
            <div className="text-center py-16">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <Bot className="h-10 w-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full animate-pulse"></div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3">
                Welcome to EduMind AI
              </h3>
              <p className="text-gray-600 text-lg max-w-md mx-auto">
                I'm here to help you with courses, programming, and answer any questions you have. Let's start chatting!
              </p>
              <div className="mt-6 flex justify-center space-x-2">
                <div className="px-4 py-2 bg-purple-100 text-purple-700 rounded-full text-sm font-medium">
                  Course Information
                </div>
                <div className="px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
                  Programming Help
                </div>
                <div className="px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm font-medium">
                  General Questions
                </div>
              </div>
            </div>
          ) : (
            <>
              {messages.map((msg, index) => (
                <div key={msg.id} className="space-y-4 animate-fadeIn">
                  {/* User Message */}
                  <div className="flex justify-end items-start space-x-4 animate-slideInRight">
                    <div className="max-w-2xl"> {/* Reduced from max-w-4xl to max-w-2xl */}
                      <div className={`bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-3 rounded-2xl rounded-br-lg shadow-lg break-words ${
                        showSendAnimation && sendingMessage === msg.message && index === messages.length - 1
                          ? 'animate-pulse transform transition-all duration-800 ease-out'
                          : ''
                      }`}>
                        <p className={`text-sm leading-relaxed whitespace-pre-wrap ${
                          showSendAnimation && sendingMessage === msg.message && index === messages.length - 1
                            ? 'animate-slideUp opacity-90'
                            : ''
                        }`}>
                          {msg.message}
                        </p>
                      </div>
                      <div className="flex items-center justify-end mt-2 space-x-2">
                        <p className="text-xs text-gray-500 font-medium">
                          You • {formatTimestamp(msg.timestamp)}
                        </p>
                        <div className={`w-2 h-2 rounded-full ${
                          showSendAnimation && sendingMessage === msg.message && index === messages.length - 1
                            ? 'bg-green-400 animate-pulse'
                            : 'bg-purple-400'
                        }`}></div>
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
                        <User className="h-6 w-6 text-white" />
                      </div>
                      <div className="w-1 h-6 bg-gradient-to-b from-purple-500 to-transparent mx-auto mt-1"></div>
                    </div>
                  </div>

                  {/* Bot Response */}
                  {msg.response && (
                    <div className="flex justify-start items-start space-x-4 animate-slideInLeft">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-gradient-to-r from-emerald-400 to-cyan-400 rounded-full flex items-center justify-center shadow-lg">
                          <Bot className="h-6 w-6 text-white" />
                        </div>
                        <div className="w-1 h-6 bg-gradient-to-b from-emerald-400 to-transparent mx-auto mt-1"></div>
                      </div>
                      <div className="flex-1 max-w-3xl">
                        <div
                          className={`bg-white border-2 px-6 py-5 rounded-3xl rounded-tl-lg shadow-lg cursor-pointer transition-all duration-300 hover:shadow-xl break-words ${
                            selectedMessage === msg.id
                              ? 'border-blue-400 bg-blue-50 shadow-xl'
                              : msg.tools_used && msg.tools_used.length > 0
                              ? 'border-purple-200 hover:border-purple-300 hover:bg-purple-50'
                              : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                          }`}
                          onClick={() => setSelectedMessage(selectedMessage === msg.id ? null : msg.id)}
                        >
                          <div className="prose prose-sm max-w-none whitespace-pre-wrap">
                            {formatAIMessage(msg.response)}
                          </div>

                          {/* Tool Call Display */}
                          {msg.tools_used && msg.tools_used.length > 0 && (
                            <ToolCallDisplay
                              tools={msg.tools_used}
                              isExpanded={expandedTools.has(msg.id)}
                              onToggle={() => {
                                const newExpanded = new Set(expandedTools);
                                if (newExpanded.has(msg.id)) {
                                  newExpanded.delete(msg.id);
                                } else {
                                  newExpanded.add(msg.id);
                                }
                                setExpandedTools(newExpanded);
                              }}
                            />
                          )}

                          <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-100">
                            <div className="flex items-center space-x-2">
                              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                              <p className="text-xs text-gray-500 font-medium">
                                AI Assistant • {formatTimestamp(msg.timestamp)}
                              </p>
                            </div>
                            {msg.tools_used && msg.tools_used.length > 0 && (
                              <div className="flex items-center space-x-2 bg-purple-100 px-3 py-1 rounded-full">
                                <Zap className="h-3 w-3 text-purple-600" />
                                <span className="text-xs text-purple-700 font-medium">
                                  {msg.tools_used.length} tool{msg.tools_used.length > 1 ? 's' : ''} used
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}

              {/* User Typing Animation - Shows where next message will appear */}
              {userTyping && (
                <div className="space-y-4 animate-fadeIn">
                  <div className="flex justify-end items-start space-x-4 animate-slideInRight">
                    <div className="max-w-2xl">
                      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-3 rounded-2xl rounded-br-lg shadow-lg opacity-70">
                        <div className="flex items-center justify-center space-x-1">
                          <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDuration: '1.0s' }}></div>
                          <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.15s', animationDuration: '1.0s' }}></div>
                          <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.3s', animationDuration: '1.0s' }}></div>
                        </div>
                      </div>
                      <div className="flex items-center justify-end mt-2 space-x-2 opacity-60">
                        <p className="text-xs text-gray-500 font-medium">
                          You • typing
                        </p>
                        <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg opacity-80">
                        <User className="h-6 w-6 text-white" />
                      </div>
                      <div className="w-1 h-6 bg-gradient-to-b from-purple-500 to-transparent mx-auto mt-1 opacity-60"></div>
                    </div>
                  </div>
                </div>
              )}


            </>
          )}

          {/* Enhanced Typing Indicator */}
          {isTyping && (
            <div className="flex justify-start items-start space-x-4 animate-slideInLeft">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-gradient-to-r from-emerald-400 to-cyan-400 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                  <Bot className="h-6 w-6 text-white" />
                </div>
                <div className="w-1 h-6 bg-gradient-to-b from-emerald-400 to-transparent mx-auto mt-1"></div>
              </div>
              <div className="bg-white border-2 border-gray-200 px-6 py-4 rounded-3xl rounded-tl-lg shadow-lg">
                {streamingResponse ? (
                  <div className="prose prose-sm max-w-none">
                    {formatAIMessage(streamingResponse)}
                    <div className="inline-block w-2 h-4 bg-purple-500 animate-pulse ml-1"></div>
                  </div>
                ) : (
                  <div className="flex items-center space-x-3">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-bounce" style={{ animationDuration: '1.2s' }}></div>
                      <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s', animationDuration: '1.2s' }}></div>
                      <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.4s', animationDuration: '1.2s' }}></div>
                    </div>
                    <span className="text-sm text-gray-500 font-medium animate-pulse">EduMind AI is thinking...</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              <p className="text-sm">{error}</p>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>



        {/* Enhanced Chat Input - Transparent background to match chat window */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 animate-slideInUp">
          <div className="max-w-5xl mx-auto">
            <ChatInput
              onSendMessage={handleSendMessage}
              isLoading={isLoading}
              placeholder="Ask me anything about courses, programming, or get help with your questions..."
              onTyping={setUserTyping}
            />
          </div>
        </div>
      </div>

      {/* Code/Tools Panel - Only show when a message with tool calls is selected */}
      {showCodePanel && selectedHasToolCalls && (
        <div className="w-96 bg-gray-50 border-l border-gray-200 flex flex-col">
          <div className="px-4 py-3 border-b border-gray-200 bg-white">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-900">Tools Used</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCodePanel(false)}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto p-4">
            {selectedMessage ? (
              <div className="space-y-4">
                {getToolsForMessage(selectedMessage).map((tool, index) => (
                  <div key={index} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                    <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">{tool.name}</h4>
                          <p className="text-xs text-gray-500">{tool.description}</p>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="p-4">
                      <pre className="text-xs text-gray-800 bg-gray-50 p-3 rounded border overflow-x-auto">
                        <code>{tool.code}</code>
                      </pre>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Code2 className="h-8 w-8 text-gray-400 mx-auto mb-3" />
                <p className="text-sm text-gray-500">
                  Click on an AI assistant message with tool calls to see the tools used
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Clear History Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showClearDialog}
        onClose={() => setShowClearDialog(false)}
        onConfirm={confirmClearHistory}
        title="Clear All Chat Data"
        message="This will permanently delete all your conversation data. This action cannot be undone."
        confirmText="Delete Everything"
        cancelText="Keep Data"
        type="danger"
        icon={<Trash2 className="h-6 w-6" />}
        details={[
          "All chat messages and conversation history",
          "Your personal information and preferences",
          "Course interests and learning goals",
          "Booking history and session data",
          "All stored memories and context"
        ]}
      />
    </div>
  );
};

export default Chat;
