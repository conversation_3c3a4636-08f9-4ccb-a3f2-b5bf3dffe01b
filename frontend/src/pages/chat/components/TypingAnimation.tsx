import React from 'react';
import { Bot, User } from 'lucide-react';

interface TypingAnimationProps {
  type: 'user' | 'assistant';
  message?: string; // For user typing, show the message being typed
}

const TypingAnimation: React.FC<TypingAnimationProps> = ({ type, message }) => {
  if (type === 'user') {
    return (
      <div className="flex justify-end mb-4">
        <div className="flex items-start space-x-3 max-w-2xl">
          <div className="bg-blue-600 text-white px-4 py-2 rounded-2xl rounded-br-md opacity-70">
            <p className="text-sm">{message}</p>
          </div>
          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
            <User className="h-4 w-4 text-white" />
          </div>
        </div>
      </div>
    );
  }

  // Assistant typing animation
  return (
    <div className="flex justify-start">
      <div className="flex items-start space-x-3 max-w-2xl">
        <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center flex-shrink-0">
          <Bot className="h-4 w-4 text-white" />
        </div>
        <div className="bg-white border px-4 py-3 rounded-2xl rounded-bl-md shadow-sm">
          <div className="flex items-center space-x-1">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <span className="text-xs text-gray-500 ml-2">AI is thinking...</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TypingAnimation;
