import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw, Trash2, Bo<PERSON>, User, Wrench, ChevronDown, ChevronUp } from 'lucide-react';
import ChatInput from './components/ChatInput';
import { useChat } from '@/hooks/useChat';
import type { ToolUsed } from '@/services';

/**
 * Advanced Chat Layout with Fixed 3-Section Design
 * ┌─────────────────────────────────┐ ← Fixed Top (Clear Chat, Refresh)
 * │         Top Controls            │
 * ├─────────────────────────────────┤
 * │                                 │ ← Scrollable Area
 * │         Messages                │   (Only this scrolls)
 * │         (Fill from bottom)      │
 * │                                 │
 * ├─────────────────────────────────┤
 * │         Input Box               │ ← Fixed Bottom (Never moves)
 * └─────────────────────────────────┘
 */

const SimplifiedChat: React.FC = () => {
  // Chat hook
  const {
    messages,
    isLoading,
    error,
    hasMoreHistory,
    isLoadingMore,
    sendMessage,
    clearHistory,
    refreshHistory,
    loadMoreHistory,
    userTyping,
    setUserTyping,
    assistantTyping,
    pendingUserMessage,
  } = useChat();

  // Local state
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  const [isClearingHistory, setIsClearingHistory] = useState(false);
  const [hasReachedTop, setHasReachedTop] = useState(false);
  const [expandedTools, setExpandedTools] = useState<Set<string>>(new Set());

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom function
  const scrollToBottom = (behavior: 'smooth' | 'auto' = 'smooth', delay = 100) => {
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior });
    }, delay);
  };

  // Handle scroll detection for infinite loading
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const { scrollTop } = target;
    
    // Check if near top (within 200px for better UX)
    const nearTop = scrollTop < 200;
    
    // Auto-load more when near top and has more history
    if (nearTop && hasMoreHistory && !isLoadingMore) {
      console.log('Auto-loading more messages...');
      loadMoreHistory();
    }
    
    // Check if reached absolute top
    if (scrollTop === 0 && !hasMoreHistory) {
      setHasReachedTop(true);
    } else {
      setHasReachedTop(false);
    }
  };

  // Auto-scroll when new messages arrive (but not when loading history)
  useEffect(() => {
    if (!isLoadingMore) {
      scrollToBottom();
    }
  }, [messages.length, userTyping, assistantTyping, pendingUserMessage, isLoadingMore]);

  // Auto-scroll when component mounts
  useEffect(() => {
    scrollToBottom('auto', 300);
  }, []);

  // Reset reached top when new messages arrive
  useEffect(() => {
    if (messages.length > 0) {
      setHasReachedTop(false);
    }
  }, [messages.length]);

  // Handle send message
  const handleSendMessage = async (message: string) => {
    if (!message.trim()) return;

    try {
      await sendMessage(message);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  // Handle clear history
  const handleClearHistory = () => {
    if (messages.length === 0) return;
    setShowClearConfirm(true);
  };

  const confirmClearHistory = async () => {
    setIsClearingHistory(true);
    try {
      await clearHistory();
      await refreshHistory();
      setShowClearConfirm(false);
    } catch (error) {
      console.error('Failed to clear chat history:', error);
    } finally {
      setIsClearingHistory(false);
    }
  };

  return (
    <div className="h-full flex flex-col bg-background">
      {/* ═══════════════════════════════════════════════════════════════ */}
      {/* FIXED TOP SECTION - Controls (Never scrolls) */}
      {/* ═══════════════════════════════════════════════════════════════ */}
      <div className="flex-shrink-0 bg-background border-b border-border">
        <div className="px-6 py-4">
          <div className="max-w-4xl mx-auto flex items-center justify-between">
            <h1 className="text-xl font-semibold text-foreground">Ambition Guru</h1>
            <div className="flex items-center space-x-3">
              <Button
                onClick={refreshHistory}
                disabled={isLoading}
                variant="ghost"
                size="sm"
                className="text-muted-foreground hover:text-foreground"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button
                onClick={handleClearHistory}
                disabled={messages.length === 0}
                variant="ghost"
                size="sm"
                className="text-destructive hover:text-destructive/80"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear Chat
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* ═══════════════════════════════════════════════════════════════ */}
      {/* SCROLLABLE MIDDLE SECTION - Messages (Only this scrolls) */}
      {/* ═══════════════════════════════════════════════════════════════ */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto bg-background min-h-0"
        onScroll={handleScroll}
        style={{ scrollBehavior: 'smooth' }}
      >
        <div className="min-h-full flex flex-col justify-end">
          <div className="max-w-4xl mx-auto px-6 py-4 w-full">
            
            {/* Loading indicator when fetching more */}
            {isLoadingMore && (
              <div className="text-center mb-6">
                <div className="flex items-center justify-center space-x-2 text-muted-foreground">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-sm">Loading more messages...</span>
                </div>
              </div>
            )}

            {/* Reached top message */}
            {hasReachedTop && !hasMoreHistory && messages.length > 0 && (
              <div className="text-center mb-6">
                <div className="text-sm text-muted-foreground bg-muted/50 rounded-lg px-4 py-2 inline-block">
                  📜 You've reached the beginning of the conversation
                </div>
              </div>
            )}

            {/* Welcome Message */}
            {messages.length === 0 && (
              <div className="text-center py-12 mb-8">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Bot className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-2xl font-semibold text-foreground mb-2">Welcome to Ambition Guru</h2>
                <p className="text-muted-foreground">Hello! How can I assist you with your academic goals today?</p>
              </div>
            )}

            {/* Messages */}
            <div className="space-y-6 pb-4">
              {messages.map((msg) => (
                <div key={msg.id} className="space-y-4">
                  {/* User Message */}
                  <div className="flex justify-end">
                    <div className="max-w-[80%] bg-primary text-primary-foreground rounded-2xl px-4 py-3">
                      <div className="flex items-start space-x-2">
                        <User className="h-4 w-4 mt-0.5 flex-shrink-0" />
                        <div className="text-sm whitespace-pre-wrap">{msg.message}</div>
                      </div>
                    </div>
                  </div>

                  {/* Assistant Message */}
                  <div className="flex justify-start">
                    <div className="max-w-[80%] bg-muted rounded-2xl px-4 py-3">
                      <div className="flex items-start space-x-2">
                        <Bot className="h-4 w-4 mt-0.5 flex-shrink-0 text-primary" />
                        <div className="text-sm text-foreground whitespace-pre-wrap">
                          {msg.response || 'Thinking...'}
                        </div>
                      </div>

                      {/* Tool Calls Display */}
                      {msg.tools_used && msg.tools_used.length > 0 && (
                        <div className="mt-3 pt-3 border-t border-border/50">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              const newExpanded = new Set(expandedTools);
                              if (newExpanded.has(msg.id)) {
                                newExpanded.delete(msg.id);
                              } else {
                                newExpanded.add(msg.id);
                              }
                              setExpandedTools(newExpanded);
                            }}
                            className="text-xs text-muted-foreground hover:text-foreground p-0 h-auto"
                          >
                            <Wrench className="h-3 w-3 mr-1" />
                            {msg.tools_used.length} tool{msg.tools_used.length > 1 ? 's' : ''} used
                            {expandedTools.has(msg.id) ? (
                              <ChevronUp className="h-3 w-3 ml-1" />
                            ) : (
                              <ChevronDown className="h-3 w-3 ml-1" />
                            )}
                          </Button>

                          {expandedTools.has(msg.id) && (
                            <div className="mt-2 space-y-2">
                              {msg.tools_used.map((tool, toolIndex) => (
                                <div key={toolIndex} className="bg-background/50 rounded-lg p-3 text-xs">
                                  <div className="font-medium text-foreground mb-1">{tool.name}</div>
                                  {tool.description && (
                                    <div className="text-muted-foreground mb-2">{tool.description}</div>
                                  )}
                                  {tool.input && Object.keys(tool.input).length > 0 && (
                                    <div className="mb-2">
                                      <div className="text-muted-foreground mb-1">Input:</div>
                                      <pre className="bg-muted/50 rounded p-2 text-xs overflow-x-auto">
                                        {JSON.stringify(tool.input, null, 2)}
                                      </pre>
                                    </div>
                                  )}
                                  {tool.output && (
                                    <div>
                                      <div className="text-muted-foreground mb-1">Output:</div>
                                      <div className="bg-muted/50 rounded p-2 text-xs">
                                        {tool.output}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* Pending user message */}
              {pendingUserMessage && (
                <div className="flex justify-end">
                  <div className="max-w-[80%] bg-primary text-primary-foreground rounded-2xl px-4 py-3">
                    <div className="flex items-start space-x-2">
                      <User className="h-4 w-4 mt-0.5 flex-shrink-0" />
                      <div className="text-sm whitespace-pre-wrap">{pendingUserMessage}</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Assistant typing indicator */}
              {assistantTyping && (
                <div className="flex justify-start">
                  <div className="max-w-[80%] bg-muted rounded-2xl px-4 py-3">
                    <div className="flex items-start space-x-2">
                      <Bot className="h-4 w-4 mt-0.5 flex-shrink-0 text-primary" />
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-lg mt-4">
                <p className="text-sm">{error}</p>
              </div>
            )}

            {/* Scroll anchor */}
            <div ref={messagesEndRef} />
          </div>
        </div>
      </div>

      {/* ═══════════════════════════════════════════════════════════════ */}
      {/* FIXED BOTTOM SECTION - Input (Never scrolls) */}
      {/* ═══════════════════════════════════════════════════════════════ */}
      <div className="flex-shrink-0 bg-background border-t border-border">
        <div className="px-6 py-4">
          <div className="max-w-4xl mx-auto">
            <ChatInput
              onSendMessage={handleSendMessage}
              isLoading={isLoading}
              placeholder="Message Ambition Guru..."
              onTyping={setUserTyping}
            />
          </div>
        </div>
      </div>

      {/* Clear Confirmation Modal */}
      {showClearConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-background rounded-lg p-6 max-w-sm mx-4">
            <h3 className="text-lg font-semibold mb-2">Clear Chat History</h3>
            <p className="text-muted-foreground mb-4">
              Are you sure you want to clear all messages? This action cannot be undone.
            </p>
            <div className="flex space-x-2">
              <Button
                onClick={() => setShowClearConfirm(false)}
                variant="outline"
                size="sm"
                disabled={isClearingHistory}
              >
                Cancel
              </Button>
              <Button
                onClick={confirmClearHistory}
                variant="destructive"
                size="sm"
                disabled={isClearingHistory}
              >
                {isClearingHistory ? 'Clearing...' : 'Clear'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimplifiedChat;
