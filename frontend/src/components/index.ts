/**
 * Components Barrel Export
 * Centralized export for all shared components
 */

// Legacy components (keeping for backward compatibility)
export { default as Button } from './Button';
export { default as Input } from './Input';
export { default as Loading } from './Loading';
export { default as Layout } from './Layout';
export { default as ConfirmDialog } from './ConfirmDialog';
export { default as ConfirmationModal } from './ConfirmationModal';

// Custom components
export { ThemeToggle } from './ThemeToggle';

// New Shadcn UI components
export { Button as UIButton } from './ui/button';
export { Input as UIInput } from './ui/input';
export { Label } from './ui/label';
export { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './ui/card';
export { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
export { Badge } from './ui/badge';
export { Separator } from './ui/separator';
export {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
